import axios, { AxiosError, AxiosResponse } from 'axios';

// Use local proxy endpoints to avoid CORS issues in development
const API_URL = '/api/proxy';
const USER_API_URL = '/api/proxy/auth/users';

// For debugging purposes - enable this to see detailed network logs
axios.interceptors.request.use(request => {
  console.log('Starting Request', {
    url: request.url,
    method: request.method,
    headers: request.headers,
    data: request.data
  });
  return request;
});

axios.interceptors.response.use(response => {
  console.log('Response:', {
    status: response.status,
    headers: response.headers,
    data: response.data
  });
  return response;
}, error => {
  console.log('Response Error:', {
    message: error.message,
    response: error.response ? {
      status: error.response.status,
      data: error.response.data
    } : 'No response',
    request: error.request ? 'Request was made but no response received' : 'Error setting up request'
  });
  return Promise.reject(error);
});

// Simple interface for the response data
interface TokenResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
}

interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  is_active: boolean;
  role_names: string[];
  organisation_id: number;
}

interface LoginData {
  email: string;
  password: string;
}

interface OrgData {
  type: 'supplier' | 'company';
  name: string;
  incorporation_date: string;
  address: string;
  telephone: string;
  contact_person: string;
  status: string;
  country?: string; // Added for new signup form design
}

interface AdminData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  telephone_number: string;
  role_names: ('c_admin' | 's_admin')[];
}

export interface SignupData {
  org_in: OrgData;
  admin_in: AdminData;
}

export interface IAuthService {
  signup(signupData: SignupData): Promise<TokenResponse>;
  login(credentials: LoginData): Promise<TokenResponse>;
  logout(): void;
  getAuthHeader(): { Authorization: string } | {};
  isAuthenticated(): boolean;
}

// Single AuthService implementation
export const AuthService: IAuthService = {
  async signup(signupData: SignupData): Promise<TokenResponse> {
    console.group('Signup Process');
    console.log('Starting signup process with data:', {
      ...signupData,
      admin_in: {
        ...signupData.admin_in,
        password: '***HIDDEN***'
      }
    });
    
    // Clear any existing tokens to avoid auth conflicts
    localStorage.removeItem('access_token');
    localStorage.removeItem('refreshToken');
    
    let response: AxiosResponse<any> | null = null;
    
    try {
      // Attempt 1: Try the most likely endpoint with the expected payload structure
      console.log('1. Attempting signup at /api/v1/auth/signup');
      try {
        response = await axios.post(`${API_URL}/auth/signup`, signupData, {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });
        
        console.log('1.1 Signup successful!', {
          status: response?.status,
          data: response?.data
        });
      } catch (error1) {
        console.log('1.2 First attempt failed:', error1);
        
        // Attempt 2: Try alternative endpoint /register
        console.log('2. Attempting signup at /api/v1/auth/register');
        try {
          response = await axios.post(`${API_URL}/auth/register`, signupData, {
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            }
          });
          
          console.log('2.1 Signup successful with /register!', {
            status: response?.status,
            data: response?.data
          });
        } catch (error2) {
          console.log('2.2 Second attempt failed:', error2);
          
          // Attempt 3: Try users endpoint
          console.log('3. Attempting signup at /api/v1/users/register');
          try {
            response = await axios.post(`${API_URL}/users/register`, signupData, {
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
              }
            });
            
            console.log('3.1 Signup successful with /users/register!', {
              status: response?.status,
              data: response?.data
            });
          } catch (error3) {
            console.log('3.2 Third attempt failed:', error3);
            
            // Attempt 4: Try simplified payload structure
            console.log('4. Attempting signup with flattened payload at /api/v1/users');
            try {
              // Create a flattened payload as fallback
              const flattenedPayload = {
                email: signupData.admin_in.email,
                password: signupData.admin_in.password,
                first_name: signupData.admin_in.first_name,
                last_name: signupData.admin_in.last_name,
                telephone_number: signupData.admin_in.telephone_number,
                role_names: signupData.admin_in.role_names,
                organisation: {
                  name: signupData.org_in.name,
                  type: signupData.org_in.type,
                  address: signupData.org_in.address,
                  telephone: signupData.org_in.telephone,
                  contact_person: signupData.org_in.contact_person,
                  status: signupData.org_in.status,
                  country: signupData.org_in.country
                }
              };
              
              response = await axios.post(`${API_URL}/users`, flattenedPayload, {
                headers: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json'
                }
              });
              
              console.log('4.1 Signup successful with flattened payload!', {
                status: response?.status,
                data: response?.data
              });
            } catch (error4) {
              console.log('4.2 Fourth attempt failed:', error4);
              
              // Final attempt: Try with native fetch API instead of axios
              console.log('5. Attempting signup with fetch API');
              try {
                const fetchResponse = await fetch(`${API_URL}/auth/signup`, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                  },
                  body: JSON.stringify(signupData)
                });
                
                if (!fetchResponse.ok) {
                  throw new Error(`HTTP error! Status: ${fetchResponse.status}`);
                }
                
                const data = await fetchResponse.json();
                console.log('5.1 Signup successful with fetch!', {
                  status: fetchResponse.status,
                  data
                });
                
                // Convert fetch response to match axios format
                response = {
                  data,
                  status: fetchResponse.status,
                  statusText: fetchResponse.statusText,
                  headers: Object.fromEntries(fetchResponse.headers.entries()) as any,
                  config: {} as any,
                  request: fetchResponse
                };
              } catch (error5) {
                console.log('5.2 Final attempt failed:', error5);
                
                // If all attempts failed, throw a comprehensive error
                const errorDetails = {
                  attempt1: error1,
                  attempt2: error2,
                  attempt3: error3,
                  attempt4: error4,
                  attempt5: error5
                };
                
                console.error('All signup attempts failed:', errorDetails);
                throw new Error('Signup failed after multiple attempts. See console for details.');
              }
            }
          }
        }
      }
      
      // If we got here, one of the attempts succeeded
      if (!response || !response.data) {
        throw new Error('Signup succeeded but returned no data');
      }
      
      console.log('6. Signup successful, response:', response.data);
      
      // Extract tokens from response
      const tokens: TokenResponse = {
        access_token: response.data.access_token || response.data.accessToken || '',
        refresh_token: response.data.refresh_token || response.data.refreshToken || '',
        token_type: response.data.token_type || response.data.tokenType || 'bearer'
      };
      
      console.log('7. Extracted tokens:', {
        hasAccessToken: !!tokens.access_token,
        hasRefreshToken: !!tokens.refresh_token,
        tokenType: tokens.token_type
      });
      
      // If signup was successful, try to automatically log in
      if (tokens.access_token) {
        console.log('8. Auto-login with tokens');
        localStorage.setItem('access_token', tokens.access_token);
        if (tokens.refresh_token) {
          localStorage.setItem('refreshToken', tokens.refresh_token);
        }
        
        // Try to get user data
        try {
          console.log('9. Fetching user data after signup');
          const userResponse = await fetch(`${API_URL}/auth/users/me`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${tokens.access_token}`,
              'Accept': 'application/json'
            }
          });
          
          if (userResponse.ok) {
            const userData = await userResponse.json();
            console.log('10. User data fetched:', userData);
            
            // Store user data in localStorage
            const transformedUser = {
              id: String(userData.id || 'unknown'),
              email: userData.email || signupData.admin_in.email,
              firstName: userData.first_name || userData.firstName || signupData.admin_in.first_name,
              lastName: userData.last_name || userData.lastName || signupData.admin_in.last_name,
              roles: Array.isArray(userData.roles) ? userData.roles : 
                   (userData.role_names || signupData.admin_in.role_names)
            };
            
            localStorage.setItem('user', JSON.stringify(transformedUser));
            console.log('11. Stored user data:', transformedUser);
          } else {
            console.warn('12. Could not fetch user data after signup:', userResponse.status);
          }
        } catch (userError) {
          console.warn('13. Error fetching user data after signup:', userError);
        }
      }
      
      console.groupEnd();
      return tokens;
    } catch (error) {
      console.error('14. Signup process failed:', error);
      
      // Clear any tokens that might have been set
      localStorage.removeItem('access_token');
      localStorage.removeItem('refreshToken');
      
      console.groupEnd();
      throw error;
    }
  },
  
  async login(credentials: LoginData): Promise<TokenResponse> {
    console.group('Login Process');
    try {
      // 1. Prepare form data for token request
      const formData = new URLSearchParams();
      formData.append('grant_type', 'password');
      formData.append('username', credentials.email);
      formData.append('password', credentials.password);
      formData.append('scope', '');
      formData.append('client_id', 'string');
      formData.append('client_secret', 'string');
      
      const formBody = formData.toString();
      
      console.log('1. Starting login for:', credentials.email);
      
      // 2. Get authentication tokens
      console.log('2. Requesting tokens from:', `${API_URL}/auth/login`);
      console.log('2.1 Request headers:', {
        'Content-Type': 'application/x-www-form-urlencoded',
        'accept': 'application/json'
      });
      console.log('2.2 Request body (password hidden):', 
        `grant_type=password&username=${credentials.email}&password=***&scope=&client_id=string&client_secret=string`);
      const loginResponse = await fetch(`${API_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'accept': 'application/json',
        },
        body: formBody,
      });

      console.log('3. Login response status:', loginResponse.status);
      
      if (!loginResponse.ok) {
        const errorText = await loginResponse.text();
        console.error('Login failed with status:', loginResponse.status);
        console.error('Response text:', errorText);
        throw new Error('Login failed');
      }

      const tokens = await loginResponse.json();
      console.log('3.1 Token response:', {
        hasAccessToken: !!tokens.access_token,
        tokenType: tokens.token_type,
        expiresIn: tokens.expires_in
      });
      
      const { access_token, refresh_token } = tokens;
      
      console.log('4. Login successful');
      console.log('Access token present:', !!access_token);
      
      // Store tokens
      localStorage.setItem('access_token', access_token);
      if (refresh_token) {
        localStorage.setItem('refreshToken', refresh_token);
      }

      // 3. Get user data
      try {
        console.log('4. Fetching user data...');
        console.log('4.1 Using access token (first 20 chars):', 
          access_token ? `${access_token.substring(0, 20)}...` : 'No token');
        
        // First, try to get current user's data directly if possible
        try {
          console.log('4.2 Trying to fetch current user data...');
          const userResponse = await fetch(`${API_URL}/auth/users/me`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${access_token}`,
              'accept': 'application/json',
            }
          });

          if (userResponse.ok) {
            const userData = await userResponse.json();
            console.log('4.3 Successfully fetched current user data:', userData);

            // Transform and store user data
            const transformedUser = {
              id: String(userData.id || 'unknown'),
              email: userData.email || credentials.email,
              firstName: userData.first_name || userData.firstName || '',
              lastName: userData.last_name || userData.lastName || '',
              roles: Array.isArray(userData.roles) ? userData.roles :
                   (userData.role_names || [])
            };

            localStorage.setItem('user', JSON.stringify(transformedUser));
            console.log('4.4 Stored user data:', transformedUser);
            console.groupEnd();
            return tokens;
          }
          console.log('4.3 /me endpoint not available or returned error, falling back to /users');
        } catch (meError) {
          console.log('4.3 Error fetching /me endpoint, falling back to /users:', meError);
        }
        
        // Fallback to fetching all users if /me endpoint fails
        console.log('5. Fetching all users...');
        const userResponse = await fetch(`${API_URL}/auth/users`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${access_token}`,
            'accept': 'application/json',
          }
        });

        const responseText = await userResponse.text();
        console.log('5.1 Raw users response:', responseText);
        
        if (!userResponse.ok) {
          console.error('Failed to fetch users:', {
            status: userResponse.status,
            statusText: userResponse.statusText,
            headers: Object.fromEntries(userResponse.headers.entries())
          });
          throw new Error(`Failed to fetch users: ${userResponse.status}`);
        }
        
        // Parse response
        const users = JSON.parse(responseText);
        console.log('6. Raw users data from API:', {
          type: typeof users,
          isArray: Array.isArray(users),
          keys: users ? Object.keys(users) : 'null/undefined',
          firstItemKeys: Array.isArray(users) && users[0] ? Object.keys(users[0]) : 'N/A',
          firstItemSample: Array.isArray(users) && users[0] ? 
            JSON.stringify(users[0], Object.getOwnPropertyNames(users[0]).filter(k => !k.startsWith('_')), 2) : 'N/A'
        });
        
        if (!Array.isArray(users)) {
          console.error('Expected array of users but got:', typeof users, users);
          throw new Error('Invalid users data format - expected array');
        }
        
        // Find current user by email (case-insensitive match)
        const currentUser = users.find((u: any) => 
          u.email && u.email.toLowerCase() === credentials.email.toLowerCase()
        );

        if (!currentUser) {
          console.warn('User not found in users list. Available emails:', 
            users.map((u: any) => u.email).filter(Boolean));
          throw new Error('User not found in system');
        }

        console.log('7. Current user found:', JSON.stringify(currentUser, null, 2));
        
        // Helper to safely extract roles from different possible structures
        const extractRoles = (user: any): string[] => {
          if (Array.isArray(user.roles)) return user.roles;
          if (Array.isArray(user.role_names)) return user.role_names;
          if (user.roles && typeof user.roles === 'string') return [user.roles];
          if (user.role_names && typeof user.role_names === 'string') return [user.role_names];
          return [];
        };

        // Transform API response to match our User interface
        const userData = {
          id: String(currentUser.id || 'unknown'),
          email: currentUser.email || credentials.email,
          // Handle both snake_case and camelCase from API
          firstName: currentUser.first_name || currentUser.firstName || '',
          lastName: currentUser.last_name || currentUser.lastName || '',
          roles: extractRoles(currentUser),
          emailVerified: currentUser.email_verified || currentUser.emailVerified || false
        };
        
        console.log('8. Transformed user data:', JSON.stringify(userData, null, 2));
        
        if (!userData.firstName && userData.email) {
          // Extract name from email as fallback
          const nameFromEmail = userData.email.split('@')[0].split('.');
          if (nameFromEmail.length > 1) {
            userData.firstName = nameFromEmail[0];
            userData.lastName = nameFromEmail[1];
          } else {
            userData.firstName = nameFromEmail[0];
          }
          console.log('9. Added name from email:', userData.firstName, userData.lastName);
        }
        
        localStorage.setItem('user', JSON.stringify(userData));
        console.log('10. Stored user data in localStorage');
      } catch (userError) {
        console.error('Error fetching user data:', userError);
        // Continue with minimal user data
        const minimalUser = {
          id: 'unknown',
          email: credentials.email,
          firstName: '',
          lastName: '',
          roles: [],
        };
        localStorage.setItem('user', JSON.stringify(minimalUser));
      }

      console.groupEnd();
      return tokens;
    } catch (error) {
      console.error('Login process failed:', error);
      throw error;
    }
  },

  logout(): void {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
  },

  getAuthHeader(): { Authorization: string } | {} {
    const token = localStorage.getItem('access_token');
    return token ? { Authorization: `Bearer ${token}` } : {};
  },

  isAuthenticated(): boolean {
    return !!localStorage.getItem('access_token');
  }
};

// Export the service as default
export default AuthService;

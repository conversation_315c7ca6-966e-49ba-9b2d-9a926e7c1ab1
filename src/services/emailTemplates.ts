import { api } from './api';

// Matches the Mongoose model IEmailTemplate
export interface ApiEmailTemplate {
  _id: string;
  name: string;
  slug: string;
  subject: string;
  html: string;
  text: string;
  variables: string[];
  isActive: boolean;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
}

// DTO for creating a new template
export interface CreateEmailTemplateDto {
  name: string;
  slug: string;
  subject: string;
  body: string;
  is_html: boolean;
  signature?: string | null;
  variables?: string[];
  // The following are deprecated but kept for now to avoid breaking other parts of the UI
  html: string; 
  text?: string;
}

// The structure of the paginated API response from the backend
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

// List all email templates with pagination and search
export const listEmailTemplates = async (search?: string, page = 1, limit = 10): Promise<PaginatedResponse<ApiEmailTemplate>> => {
  const response = await api.get<PaginatedResponse<ApiEmailTemplate>>('/email/templates', {
    params: { search, page, limit },
  });
  return response.data;
};

// Get a specific email template by slug
export const getEmailTemplate = async (slug: string): Promise<ApiEmailTemplate> => {
  const response = await api.get<ApiEmailTemplate>(`/email/templates/${slug}`);
  return response.data;
};

// Create a new email template
export const createEmailTemplate = async (data: CreateEmailTemplateDto): Promise<ApiEmailTemplate> => {
  const response = await api.post<ApiEmailTemplate>('/email/templates', data);
  return response.data;
};

// Update an existing email template
export const updateEmailTemplate = async (slug: string, data: Partial<CreateEmailTemplateDto>): Promise<ApiEmailTemplate> => {
  const response = await api.put<ApiEmailTemplate>(`/email/templates/${slug}`, data);
  return response.data;
};

// Delete an email template
export const deleteEmailTemplate = async (slug: string): Promise<void> => {
  await api.delete(`/email/templates/${slug}`);
};

// --- Client-side validation and utility functions ---

// Validate template slug format
export const validateSlug = (slug: string): boolean => {
  const regex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return regex.test(slug);
};

// Generate a slug from a subject line
export const generateSlug = (subject: string): string => {
  return subject
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // remove non-word characters
    .replace(/\s+/g, '-') // replace spaces with hyphens
    .replace(/--+/g, '-') // replace multiple hyphens with a single one
    .substring(0, 100) // Limit length
    .replace(/-+$/, ''); // Remove trailing hyphens
};

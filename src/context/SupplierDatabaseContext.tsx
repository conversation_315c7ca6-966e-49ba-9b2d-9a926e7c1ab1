'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

interface SupplierDatabaseContextType {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  checkedPrimary: Record<string, boolean>;
  handlePrimaryChange: (filter: string) => void;
  checkedCategories: Record<string, boolean>;
  handleCategoryChange: (category: string) => void;
  clearFilters: () => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  sortConfig: { key: string; direction: 'ascending' | 'descending' } | null;
  setSortConfig: (config: { key: string; direction: 'ascending' | 'descending' } | null) => void;
}

const SupplierDatabaseContext = createContext<SupplierDatabaseContextType | undefined>(undefined);

export const SupplierDatabaseProvider = ({ children }: { children: ReactNode }) => {
  const [activeTab, setActiveTab] = useState('All suppliers');
  const [checkedPrimary, setCheckedPrimary] = useState<Record<string, boolean>>({});
  const [checkedCategories, setCheckedCategories] = useState<Record<string, boolean>>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'ascending' | 'descending' } | null>(null);

  const handlePrimaryChange = (filter: string) => {
    setCheckedPrimary(prev => ({ ...prev, [filter]: !prev[filter] }));
  };

  const handleCategoryChange = (category: string) => {
    setCheckedCategories(prev => ({ ...prev, [category]: !prev[category] }));
  };

  const clearFilters = () => {
    setCheckedPrimary({});
    setCheckedCategories({});
  };

  return (
    <SupplierDatabaseContext.Provider
      value={{
        activeTab,
        setActiveTab,
        checkedPrimary,
        handlePrimaryChange,
        checkedCategories,
        handleCategoryChange,
        clearFilters,
        searchTerm,
        setSearchTerm,
        sortConfig,
        setSortConfig,
      }}
    >
      {children}
    </SupplierDatabaseContext.Provider>
  );
};

export const useSupplierDatabase = () => {
  const context = useContext(SupplierDatabaseContext);
  if (context === undefined) {
    throw new Error('useSupplierDatabase must be used within a SupplierDatabaseProvider');
  }
  return context;
};

'use client';

import React, { createContext, useContext, ReactNode } from 'react';

interface InquiriesContextType {
  // Add any context values you'll need for the inquiries feature
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  // Add more context values as needed
}

const InquiriesContext = createContext<InquiriesContextType | undefined>(undefined);

export function InquiriesProvider({ children }: { children: ReactNode }) {
  const [searchTerm, setSearchTerm] = React.useState('');
  
  const value = {
    searchTerm,
    setSearchTerm,
  };

  return (
    <InquiriesContext.Provider value={value}>
      {children}
    </InquiriesContext.Provider>
  );
}

export function useInquiries() {
  const context = useContext(InquiriesContext);
  if (context === undefined) {
    throw new Error('useInquiries must be used within an InquiriesProvider');
  }
  return context;
}

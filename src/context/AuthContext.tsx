'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { AuthService } from '@/services/auth.service';
import { toast } from 'sonner';

interface OrgData {
  name: string;
  type: 'company' | 'supplier';
  address: string;
  telephone: string;
  country: string;
  incorporation_date: string;
  contact_person: string;
  status: string;
}

interface AdminData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  telephone_number: string;
  role_names: ('c_admin' | 's_admin')[];
}

export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  roles: string[];
  avatar?: string;
  emailVerified?: boolean;
}

export interface SignupData {
  org_in: Omit<OrgData, 'type'> & { type: 'company' | 'supplier' };
  admin_in: AdminData;
}

interface AuthContextType {
  isAuthenticated: boolean | null; // null means loading/initializing
  login: (email: string, password: string) => Promise<void>;
  signup: (userData: SignupData) => Promise<void>;
  logout: () => void;
  user: User | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null); // null means loading/initializing
  const [user, setUser] = useState<User | null>(null);

  // Initialize auth state from localStorage
  useEffect(() => {
    console.log('AuthContext: Initializing auth state from localStorage');
    const token = typeof window !== 'undefined' ? localStorage.getItem('access_token') : null;
    
    if (token) {
      console.log('AuthContext: Found access_token in localStorage');
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        try {
          const parsedUser = JSON.parse(storedUser);
          console.log('AuthContext: Found and parsed user from localStorage', parsedUser.email);
          setUser(parsedUser);
        } catch (e) {
          console.error('Failed to parse user from localStorage', e);
          localStorage.removeItem('user'); // Clear corrupted user data
        }
      } else {
        console.log('AuthContext: No user found in localStorage despite having token');
      }
      setIsAuthenticated(true);
    } else {
      console.log('AuthContext: No access_token found in localStorage, not authenticated');
      setIsAuthenticated(false);
      setUser(null);
    }
  }, []);

  const login = async (email: string, password: string) => {
    try {
      console.log('AuthContext: Attempting login for', email);
      const response = await AuthService.login({ email, password });
      
      if (response.access_token) {
        console.log('AuthContext: Login successful, got access_token');
        // Double-check that token was stored properly
        const storedToken = localStorage.getItem('access_token');
        if (!storedToken) {
          console.error('AuthContext: Token not found in localStorage after login');
          // Store it again just to be safe
          localStorage.setItem('access_token', response.access_token);
        }
        
        // Get user data from localStorage (AuthService.login should have stored it)
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
          try {
            const parsedUser = JSON.parse(storedUser);
            console.log('AuthContext: Found user in localStorage after login', parsedUser.email);

            // Check if email verification is required (for production, you might want to enforce this)
            if (parsedUser.emailVerified === false) {
              console.warn('AuthContext: User email not verified');
              // For now, we'll allow login but you can uncomment below to enforce verification
              // throw new Error('Please verify your email address before signing in. Check your inbox for a verification link.');
            }

            setUser(parsedUser);
          } catch (e) {
            console.error('Failed to parse user from localStorage after login', e);
          }
        } else {
          console.warn('AuthContext: No user found in localStorage after login');
          // Create minimal user object with email
          const minimalUser = {
            id: 'unknown',
            email: email,
            firstName: '',
            lastName: '',
            roles: [],
            emailVerified: false,
          };
          localStorage.setItem('user', JSON.stringify(minimalUser));
          setUser(minimalUser);
        }
        
        // Update authentication state
        setIsAuthenticated(true);
      } else {
        console.error('AuthContext: Login response missing access_token');
        setIsAuthenticated(false);
      }
    } catch (error) {
      console.error('AuthContext: Login failed:', error);
      setIsAuthenticated(false);
      setUser(null);
      throw error;
    }
  };

  const signup = async (userData: SignupData) => {
    try {
      const response = await AuthService.signup(userData);
      if (response.access_token) {
        // AuthService.signup now handles storing access_token and user in localStorage
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
          try {
            setUser(JSON.parse(storedUser));
          } catch (e) {
            console.error('Failed to parse user from localStorage after signup', e);
          }
        }
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error('Signup failed:', error);
      setIsAuthenticated(false);
      setUser(null);
      throw error;
    }
  };

  const logout = () => {
    console.log('AuthContext: Logging out user');
    AuthService.logout();
    setIsAuthenticated(false);
    setUser(null);
    toast.success('Logged out successfully!');
    console.log('AuthContext: User logged out successfully');
  };

  const value = {
    isAuthenticated,
    login,
    signup,
    logout,
    user
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;

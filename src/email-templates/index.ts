export * from './supplier-invitation';
export * from './password-reset';
export * from './welcome';

// Types
export interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

// Template names for reference
export const TEMPLATE_NAMES = {
  SUPPLIER_INVITATION: 'supplier-invitation',
  PASSWORD_RESET: 'password-reset',
  WELCOME: 'welcome',
} as const;

export type TemplateName = typeof TEMPLATE_NAMES[keyof typeof TEMPLATE_NAMES];

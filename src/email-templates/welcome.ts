interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

export const welcomeTemplate = (data: {
  userName: string;
  loginUrl: string;
  supportEmail: string;
  helpUrl?: string;
}): EmailTemplate => ({
  subject: 'Welcome to Ascension!',
  
  html: `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #18546c; margin-bottom: 10px;">Welcome to Ascension!</h1>
        <p style="color: #666; font-size: 18px;">We're thrilled to have you on board</p>
      </div>
      
      <p>Dear ${data.userName},</p>
      
      <p>Thank you for joining Ascension! Your account has been successfully created and is ready to use.</p>
      
      <div style="background-color: #f0f7fb; border-left: 4px solid #3498db; padding: 15px; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #18546c;">Getting Started</h3>
        <ol style="padding-left: 20px;">
          <li>Log in to your account using the button below</li>
          <li>Complete your profile information</li>
          <li>Explore the dashboard and features</li>
        </ol>
      </div>
      
      <div style="text-align: center; margin: 30px 0;">
        <a href="${data.loginUrl}" 
           style="background-color: #18546c; color: white; padding: 12px 30px; 
                  text-decoration: none; border-radius: 4px; font-weight: bold;
                  display: inline-block; font-size: 16px;">
          Log In to Your Account
        </a>
      </div>
      
      <div style="margin: 30px 0; padding: 20px; background-color: #f9f9f9; border-radius: 5px;">
        <h3 style="margin-top: 0; color: #18546c;">Need Help?</h3>
        <p>Our support team is here to help you with any questions or issues you might have.</p>
        <p>
          <strong>Email:</strong> 
          <a href="mailto:${data.supportEmail}" style="color: #18546c; text-decoration: none;">
            ${data.supportEmail}
          </a>
        </p>
        ${data.helpUrl ? `
          <p>
            <strong>Help Center:</strong> 
            <a href="${data.helpUrl}" style="color: #18546c; text-decoration: none;">
              Visit Help Center
            </a>
          </p>
        ` : ''}
      </div>
      
      <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center;">
        <p style="color: #666; font-size: 14px;">
          &copy; ${new Date().getFullYear()} Ascension. All rights reserved.
        </p>
      </div>
    </div>
  `,
  
  text: `
    WELCOME TO ASCENSION
    ===================
    
    Dear ${data.userName},
    
    Thank you for joining Ascension! Your account has been successfully created and is ready to use.
    
    GETTING STARTED:
    1. Log in to your account at: ${data.loginUrl}
    2. Complete your profile information
    3. Explore the dashboard and features
    
    NEED HELP?
    Our support team is here to help you with any questions or issues you might have.
    
    Email: ${data.supportEmail}
    ${data.helpUrl ? `Help Center: ${data.helpUrl}` : ''}
    
    © ${new Date().getFullYear()} Ascension. All rights reserved.
  `
});

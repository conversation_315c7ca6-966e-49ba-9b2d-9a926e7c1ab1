interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

export const passwordResetTemplate = (data: {
  resetUrl: string;
  expiresIn?: string;
  userEmail: string;
}): EmailTemplate => ({
  subject: 'Reset Your Ascension Password',
  
  html: `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="text-align: center; margin-bottom: 20px;">
        <h1 style="color: #18546c;">Password Reset Request</h1>
      </div>
      
      <p>Hello,</p>
      
      <p>We received a request to reset the password for your Ascension account (${data.userEmail}).</p>
      
      <div style="text-align: center; margin: 30px 0;">
        <a href="${data.resetUrl}" 
           style="background-color: #18546c; color: white; padding: 12px 25px; 
                  text-decoration: none; border-radius: 4px; font-weight: bold;
                  display: inline-block;">
          Reset Password
        </a>
      </div>
      
      <p style="font-size: 14px; color: #666; text-align: center;">
        Or copy and paste this link into your browser:<br>
        <span style="word-break: break-all; color: #18546c;">${data.resetUrl}</span>
      </p>
      
      <div style="margin-top: 20px; padding: 15px; background-color: #fff8e1; border-left: 4px solid #ffc107;">
        <p style="margin: 0; color: #5d4037;">
          <strong>Note:</strong> This link will expire in ${data.expiresIn || '1 hour'}.
          If you didn't request a password reset, please ignore this email.
        </p>
      </div>
      
      <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
        <p>For security reasons, this link will expire after the time period has passed.</p>
        <p>Best regards,<br>The Ascension Team</p>
      </div>
    </div>
  `,
  
  text: `
    PASSWORD RESET REQUEST
    =====================
    
    Hello,
    
    We received a request to reset the password for your Ascension account (${data.userEmail}).
    
    To reset your password, please visit:
    ${data.resetUrl}
    
    Note: This link will expire in ${data.expiresIn || '1 hour'}.
    
    If you didn't request a password reset, you can safely ignore this email.
    
    For security reasons, this link will expire after the time period has passed.
    
    Best regards,
    The Ascension Team
  `
});

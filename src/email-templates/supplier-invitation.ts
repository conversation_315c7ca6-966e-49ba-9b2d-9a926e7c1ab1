interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

export const supplierInvitationTemplate = (data: {
  invitationId: string;
  organizationName: string;
  role: string;
  expiryDate: string;
  invitationUrl: string;
}): EmailTemplate => ({
  subject: `You've been invited to join ${data.organizationName} on Ascension`,
  
  html: `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="text-align: center; margin-bottom: 20px;">
        <h1 style="color: #18546c;">Invitation to Join Ascension</h1>
      </div>
      
      <p>Hello,</p>
      
      <p>You have been invited to join <strong>${data.organizationName}</strong> on Ascension as a <strong>${data.role}</strong>.</p>
      
      <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #18546c;">Invitation Details</h3>
        <p><strong>Invitation ID:</strong> ${data.invitationId}</p>
        <p><strong>Organization:</strong> ${data.organizationName}</p>
        <p><strong>Role:</strong> ${data.role}</p>
        <p><strong>Expires on:</strong> ${data.expiryDate}</p>
      </div>
      
      <div style="text-align: center; margin: 30px 0;">
        <a href="${data.invitationUrl}" 
           style="background-color: #18546c; color: white; padding: 12px 25px; 
                  text-decoration: none; border-radius: 4px; font-weight: bold;
                  display: inline-block;">
          Accept Invitation
        </a>
      </div>
      
      <p style="font-size: 14px; color: #666; text-align: center;">
        Or copy and paste this link into your browser:<br>
        <span style="word-break: break-all; color: #18546c;">${data.invitationUrl}</span>
      </p>
      
      <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
        <p>If you didn't request this invitation, you can safely ignore this email.</p>
        <p>Best regards,<br>The Ascension Team</p>
      </div>
    </div>
  `,
  
  text: `
    INVITATION TO JOIN ASCENSION
    ==========================
    
    Hello,
    
    You have been invited to join ${data.organizationName} on Ascension as a ${data.role}.
    
    INVITATION DETAILS:
    - Invitation ID: ${data.invitationId}
    - Organization: ${data.organizationName}
    - Role: ${data.role}
    - Expires on: ${data.expiryDate}
    
    To accept this invitation, please visit:
    ${data.invitationUrl}
    
    If you didn't request this invitation, you can safely ignore this email.
    
    Best regards,
    The Ascension Team
  `
});

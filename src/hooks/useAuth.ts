'use client';

import { useSession } from 'next-auth/react';

export function useAuth(required = false) {
  const { data: session, status } = useSession();
  const isLoading = status === 'loading';
  const isAuthenticated = !!session?.user;

  if (required && !isLoading && !isAuthenticated) {
    // Optionally trigger a redirect here or handle it in the component
  }

  return {
    user: session?.user || null,
    isAuthenticated,
    isLoading,
  };
}

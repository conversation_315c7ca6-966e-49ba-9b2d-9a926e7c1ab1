'use client';

import { useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'sonner';

export default function EmailVerificationBanner() {
  const { user } = useAuth();
  const [isResending, setIsResending] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  // Don't show banner if user is verified, not logged in, or banner is dismissed
  if (!user || user.emailVerified !== false || isDismissed) {
    return null;
  }

  const handleResendEmail = async () => {
    if (!user?.email) return;

    setIsResending(true);
    try {
      const response = await fetch('/api/auth/resend-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: user.email }),
      });

      if (response.ok) {
        toast.success('Verification email sent! Please check your inbox.');
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || 'Failed to send verification email');
      }
    } catch (error) {
      console.error('Error resending verification email:', error);
      toast.error('Failed to send verification email');
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-400 p-4 mb-6">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
            Email Verification Required
          </h3>
          <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
            <p>
              Please verify your email address to secure your account and access all features. 
              We sent a verification link to <strong>{user.email}</strong>.
            </p>
          </div>
          <div className="mt-4 flex space-x-3">
            <button
              onClick={handleResendEmail}
              disabled={isResending}
              className="bg-yellow-100 dark:bg-yellow-800 px-3 py-2 rounded-md text-sm font-medium text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isResending ? 'Sending...' : 'Resend Email'}
            </button>
            <button
              onClick={() => setIsDismissed(true)}
              className="text-yellow-700 dark:text-yellow-300 hover:text-yellow-600 dark:hover:text-yellow-200 text-sm font-medium transition-colors"
            >
              Dismiss
            </button>
          </div>
        </div>
        <div className="flex-shrink-0 ml-4">
          <button
            onClick={() => setIsDismissed(true)}
            className="text-yellow-400 hover:text-yellow-600 dark:hover:text-yellow-200 transition-colors"
          >
            <span className="sr-only">Dismiss</span>
            <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}

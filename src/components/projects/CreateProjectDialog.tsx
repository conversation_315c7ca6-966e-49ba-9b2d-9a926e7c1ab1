import React, { useState } from 'react';
import { FiX, FiCalendar, FiUsers, FiPackage } from 'react-icons/fi';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface CreateProjectDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onCreate: (project: {
    name: string;
    description: string;
    targetDate: string;
    suppliersCount: number;
    itemsCount: number;
  }) => void;
}

export function CreateProjectDialog({ isOpen, onClose, onCreate }: CreateProjectDialogProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    targetDate: '',
    suppliersCount: 1,
    itemsCount: 1,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      onCreate({
        ...formData,
        suppliersCount: Number(formData.suppliersCount),
        itemsCount: Number(formData.itemsCount),
      });
      setIsSubmitting(false);
      onClose();
    }, 1000);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const numValue = Math.max(1, parseInt(value) || 1);
    setFormData(prev => ({
      ...prev,
      [name]: numValue
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <div className="flex justify-between items-center">
            <DialogTitle>Create New Project</DialogTitle>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <FiX className="h-5 w-5" />
            </Button>
          </div>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Project Name</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="e.g. Office Supplies Q3 2023"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Brief description of the project"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="targetDate">
                <FiCalendar className="inline mr-2 h-4 w-4" />
                Target Date
              </Label>
              <Input
                id="targetDate"
                name="targetDate"
                type="date"
                value={formData.targetDate}
                onChange={handleChange}
                required
                min={new Date().toISOString().split('T')[0]}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="suppliersCount">
                <FiUsers className="inline mr-2 h-4 w-4" />
                Suppliers
              </Label>
              <Input
                id="suppliersCount"
                name="suppliersCount"
                type="number"
                min="1"
                value={formData.suppliersCount}
                onChange={handleNumberChange}
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="itemsCount">
              <FiPackage className="inline mr-2 h-4 w-4" />
              Estimated Items
            </Label>
            <Input
              id="itemsCount"
              name="itemsCount"
              type="number"
              min="1"
              value={formData.itemsCount}
              onChange={handleNumberChange}
              required
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
              className="border border-[#18546c] bg-transparent text-[#18546c] hover:bg-[#18546c] hover:text-white dark:border-slate-500 dark:text-slate-300 dark:hover:bg-[#18546c] dark:hover:text-white shadow hover:shadow-md transition-colors duration-200 text-sm"
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="bg-gray-100 text-[#18546c] hover:bg-[#18546c] hover:text-white dark:bg-slate-800 dark:text-white dark:hover:bg-[#18546c] transition-colors duration-200 shadow hover:shadow-md text-sm"
            >
              {isSubmitting ? 'Creating...' : 'Create Project'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}

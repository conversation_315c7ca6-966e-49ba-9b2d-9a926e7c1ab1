import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiArrowUp, FiArrowDown, FiChevronDown } from 'react-icons/fi';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

type SortField = 'name' | 'status' | 'lastUpdated' | 'progress' | 'daysRemaining';
type SortDirection = 'asc' | 'desc';

interface SortOption {
  field: SortField;
  label: string;
}

interface ProjectsHeaderProps {
  searchQuery: string;
  onSearchChange: (value: string) => void;
  onFilterChange: (filter: string) => void;
  onSortChange: (field: SortField, direction: SortDirection) => void;
  sortField: SortField;
  sortDirection: SortDirection;
  onCreateProject: () => void;
}

const sortOptions: SortOption[] = [
  { field: 'name', label: 'Name' },
  { field: 'status', label: 'Status' },
  { field: 'lastUpdated', label: 'Last Updated' },
  { field: 'progress', label: 'Progress' },
  { field: 'daysRemaining', label: 'Days Remaining' },
];

export function ProjectsHeader({
  searchQuery,
  onSearchChange,
  onFilterChange,
  onSortChange,
  sortField,
  sortDirection,
  onCreateProject,
}: ProjectsHeaderProps) {
  const [isSortOpen, setIsSortOpen] = React.useState(false);

  const handleSort = (field: SortField) => {
    if (field === sortField) {
      onSortChange(field, sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      onSortChange(field, 'asc');
    }
    setIsSortOpen(false);
  };

  const currentSortLabel = sortOptions.find(opt => opt.field === sortField)?.label || 'Sort';
  const SortIcon = sortDirection === 'asc' ? FiArrowUp : FiArrowDown;
  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
      <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Projects</h1>
      
      <div className="w-full sm:w-auto flex flex-col sm:flex-row gap-3">
        <div className="relative">
          <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            type="text"
            placeholder="Search projects..."
            className="pl-10 w-full sm:w-64"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
          />
        </div>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="flex items-center gap-2">
              <FiFilter className="h-4 w-4" />
              <span>Filter</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => onFilterChange('all')}>
              All Projects
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onFilterChange('active')}>
              Active
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onFilterChange('inactive')}>
              Inactive
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <DropdownMenu open={isSortOpen} onOpenChange={setIsSortOpen}>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="flex items-center gap-2">
              <SortIcon className="h-4 w-4" />
              <span>{currentSortLabel}</span>
              <FiChevronDown className="h-4 w-4 ml-1" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {sortOptions.map((option) => (
              <DropdownMenuItem 
                key={option.field}
                onClick={() => handleSort(option.field)}
                className="flex justify-between items-center"
              >
                <span>{option.label}</span>
                {sortField === option.field && (
                  <SortIcon className="h-3.5 w-3.5 ml-2" />
                )}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
        
        <Button onClick={onCreateProject} className="bg-gray-100 text-[#18546c] hover:bg-[#18546c] hover:text-white dark:bg-slate-800 dark:text-white dark:hover:bg-[#18546c] transition-colors duration-200 shadow hover:shadow-md text-sm w-full sm:w-auto">
          <span className="mr-1.5 text-base">+</span> Create Project
        </Button>
      </div>
    </div>
  );
}

import React from 'react';
import { FiMoreVertical, FiClock } from 'react-icons/fi';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';

interface ProjectCardProps {
  id: string;
  name: string;
  status: 'Active' | 'Inactive';
  suppliersCount: number;
  itemsCount: number;
  progress: number;
  daysRemaining: number;
  lastUpdated: string;
}

export function ProjectCard({
  id,
  name,
  status,
  suppliersCount,
  itemsCount,
  progress,
  daysRemaining,
  lastUpdated,
}: ProjectCardProps) {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div className="p-4">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-medium text-gray-900 dark:text-white">{name}</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">ID: {id}</p>
          </div>
          <div className="flex items-center space-x-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              status === 'Active' 
                ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' 
                : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
            }`}>
              {status}
            </span>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <FiMoreVertical className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-gray-500 dark:text-gray-400">Suppliers</p>
            <p className="font-medium">{suppliersCount}</p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Items</p>
            <p className="font-medium">{itemsCount}</p>
          </div>
        </div>

        <div className="mt-4">
          <div className="flex justify-between text-sm mb-1">
            <span className="text-gray-500 dark:text-gray-400">Progress</span>
            <span className="font-medium">{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        <div className="mt-4 flex items-center justify-between text-sm">
          <div className="flex items-center text-gray-500 dark:text-gray-400">
            <FiClock className="mr-1 h-4 w-4" />
            <span>{daysRemaining} days remaining</span>
          </div>
          <span className="text-gray-400 dark:text-gray-500 text-xs">
            Updated {lastUpdated}
          </span>
        </div>
      </div>
    </div>
  );
}

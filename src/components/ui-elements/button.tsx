import { cva, VariantProps } from "class-variance-authority";
import type { HTMLAttributes } from "react";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2.5 text-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary/50",
  {
    variants: {
      variant: {
        primary: "bg-primary text-white hover:bg-primary/90 shadow-sm hover:shadow-md",
        green: "bg-green text-white hover:bg-green/90 shadow-sm hover:shadow-md",
        dark: "bg-dark text-white hover:bg-dark/90 dark:bg-white/10 dark:hover:bg-white/20 shadow-sm hover:shadow-md",
        outlinePrimary:
          "border border-primary hover:bg-primary/5 text-primary hover:shadow-sm focus:ring-primary/30",
        outlineGreen: "border border-green hover:bg-green/5 text-green hover:shadow-sm focus:ring-green/30",
        outlineDark:
          "border border-dark hover:bg-dark/5 text-dark dark:hover:bg-white/10 dark:border-white/25 dark:text-white hover:shadow-sm focus:ring-white/30",
      },
      shape: {
        default: "",
        rounded: "rounded-[5px]",
        full: "rounded-full",
      },
      size: {
        default: "py-3 px-8 text-sm font-semibold rounded-md",
        small: "py-2 px-4 text-sm font-medium rounded",
        large: "py-3.5 px-10 text-base font-semibold rounded-lg",
      },
    },
    defaultVariants: {
      variant: "primary",
      shape: "default",
      size: "default",
    },
  },
);

type ButtonProps = HTMLAttributes<HTMLButtonElement> &
  VariantProps<typeof buttonVariants> & {
    label: string;
    icon?: React.ReactNode;
  };

export function Button({
  label,
  icon,
  variant,
  shape,
  size,
  className,
  ...props
}: ButtonProps) {
  return (
    <button
      className={buttonVariants({ variant, shape, size, className })}
      {...props}
    >
      {icon && <span>{icon}</span>}
      {label}
    </button>
  );
}

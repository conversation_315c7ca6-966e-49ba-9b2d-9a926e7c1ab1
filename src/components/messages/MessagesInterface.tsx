'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  InboxIcon,
  PaperAirplaneIcon,
  StarIcon,
  TrashIcon,
  PencilIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon,
  EnvelopeIcon as MailIcon,
} from '@heroicons/react/24/solid';
import { EmailListItem } from './EmailListItem';
import { EmailDetail } from './EmailDetail';
import { ComposeEmail } from './ComposeEmail';
import { cn } from "@/lib/utils";

interface Email {
  id: string;
  from: string;
  to: string[];
  subject: string;
  preview: string;
  body: string;
  date: Date;
  isRead: boolean;
  isStarred: boolean;
  hasAttachment: boolean;
  folder: 'inbox' | 'sent' | 'starred' | 'trash';
}

const mockEmails: Email[] = [
  {
    id: '1',
    from: '<EMAIL>',
    to: ['<EMAIL>'],
    subject: 'Weekly Team Update',
    preview: 'Here\'s what happened this week in our team...',
    body: 'Hello,\n\nHere\'s a summary of what happened this week in our team:\n\n1. Project A: Completed the initial design phase\n2. Project B: Development is 75% complete\n3. Team meeting scheduled for next Monday at 10 AM\n\nBest regards,\nJohn',
    date: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    isRead: false,
    isStarred: true,
    hasAttachment: true,
    folder: 'inbox',
  },
  {
    id: '2',
    from: '<EMAIL>',
    to: ['<EMAIL>', '<EMAIL>'],
    subject: 'Project Timeline Update',
    preview: 'I\'ve updated the project timeline with the latest...',
    body: 'Hi team,\n\nI\'ve updated the project timeline with the latest estimates from the development team. We\'re still on track for the Q3 release.\n\nPlease review the attached document and let me know if you have any questions.\n\nBest,\nAlice',
    date: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
    isRead: true,
    isStarred: false,
    hasAttachment: true,
    folder: 'inbox',
  },
  // Add more mock emails as needed
];

export function MessagesInterface() {
  const [activeTab, setActiveTab] = useState('inbox');
  const [selectedEmail, setSelectedEmail] = useState<Email | null>(null);
  const [showCompose, setShowCompose] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [emails, setEmails] = useState<Email[]>(mockEmails);
  const [selectedEmails, setSelectedEmails] = useState<string[]>([]);
  const [isMobile, setIsMobile] = useState(false);

  // Filter emails based on active tab and search query
  const filteredEmails = emails.filter(email => {
    const matchesTab = email.folder === activeTab || 
                     (activeTab === 'starred' && email.isStarred);
    const matchesSearch = searchQuery === '' || 
                         email.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         email.from.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         email.preview.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesTab && matchesSearch;
  });

  const handleEmailClick = (email: Email) => {
    setSelectedEmail(email);
    // Mark as read when opened
    if (!email.isRead) {
      setEmails(emails.map(e => 
        e.id === email.id ? { ...e, isRead: true } : e
      ));
    }
  };

  const handleSendEmail = (email: { to: string; subject: string; body: string }) => {
    const newEmail: Email = {
      id: Date.now().toString(),
      from: '<EMAIL>',
      to: [email.to],
      subject: email.subject,
      preview: email.body.substring(0, 100) + '...',
      body: email.body,
      date: new Date(),
      isRead: true,
      isStarred: false,
      hasAttachment: false,
      folder: 'sent',
    };
    setEmails([newEmail, ...emails]);
    setShowCompose(false);
  };

  const handleStarEmail = (emailId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setEmails(emails.map(email => 
      email.id === emailId ? { ...email, isStarred: !email.isStarred } : email
    ));
  };

  const handleDeleteEmail = (emailId: string) => {
    setEmails(emails.map(email => 
      email.id === emailId ? { ...email, folder: 'trash' as const } : email
    ));
    if (selectedEmail?.id === emailId) {
      setSelectedEmail(null);
    }
  };

  const handleBackToList = () => {
    setSelectedEmail(null);
  };

  // Check if mobile view
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    // Initial check
    checkIfMobile();
    
    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);
    
    // Cleanup function
    return () => {
      window.removeEventListener('resize', checkIfMobile);
    };
  }, []); // Empty dependency array means this runs once on mount

  return (
    <div className="flex h-full flex-col bg-white dark:bg-gray-900">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-gray-200 p-4 dark:border-gray-700">
        <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
          {selectedEmail ? 'Message' : 'Messages'}
        </h1>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowCompose(true)}
            className="hidden md:flex"
          >
            <PencilIcon className="mr-2 h-4 w-4" />
            Compose
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => window.location.reload()}
          >
            <ArrowPathIcon className="h-5 w-5" />
          </Button>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        {(!isMobile || !selectedEmail) && (
          <div className="w-full border-r border-gray-200 md:w-80 dark:border-gray-700">
            {/* Search */}
            <div className="p-4">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search messages..."
                  className="w-full pl-9"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            {/* Compose button for mobile */}
            <div className="px-4 pb-4 md:hidden">
              <Button
                onClick={() => setShowCompose(true)}
                className="w-full"
              >
                <PencilIcon className="mr-2 h-4 w-4" />
                Compose
              </Button>
            </div>

            {/* Folders */}
            <Tabs 
              value={activeTab} 
              onValueChange={setActiveTab}
              className="px-2"
            >
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="inbox" className="text-xs">
                  <InboxIcon className="mr-1 h-4 w-4" />
                  <span className="hidden sm:inline">Inbox</span>
                </TabsTrigger>
                <TabsTrigger value="starred" className="text-xs">
                  <StarIcon className="mr-1 h-4 w-4" />
                  <span className="hidden sm:inline">Starred</span>
                </TabsTrigger>
                <TabsTrigger value="sent" className="text-xs">
                  <PaperAirplaneIcon className="mr-1 h-4 w-4" />
                  <span className="hidden sm:inline">Sent</span>
                </TabsTrigger>
                <TabsTrigger value="trash" className="text-xs">
                  <TrashIcon className="mr-1 h-4 w-4" />
                  <span className="hidden sm:inline">Trash</span>
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Email List */}
            <div className="h-[calc(100%-180px)] overflow-y-auto">
              {filteredEmails.length === 0 ? (
                <div className="flex h-full flex-col items-center justify-center p-8 text-center">
                  <InboxIcon className="h-12 w-12 text-gray-300" />
                  <p className="mt-2 text-sm text-gray-500">
                    No messages in this folder
                  </p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredEmails.map((email) => (
                    <div
                      key={email.id}
                      className={cn(
                        'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800',
                        selectedEmail?.id === email.id && 'bg-blue-50 dark:bg-gray-800'
                      )}
                      onClick={() => handleEmailClick(email)}
                    >
                      <EmailListItem
                        id={email.id}
                        from={email.from}
                        subject={email.subject}
                        preview={email.preview}
                        time={email.date}
                        isRead={email.isRead}
                        isStarred={email.isStarred}
                        hasAttachment={email.hasAttachment}
                        isSelected={selectedEmail?.id === email.id}
                        onClick={() => handleEmailClick(email)}
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Email Detail View */}
        <div className={cn(
          'flex-1',
          isMobile && !selectedEmail && 'hidden',
          isMobile && selectedEmail && 'w-full'
        )}>
          {selectedEmail ? (
            <EmailDetail 
              email={{
                ...selectedEmail,
                to: selectedEmail.to
              }} 
              onBack={handleBackToList}
            />
          ) : (
            <div className="flex h-full flex-col items-center justify-center p-8 text-center">
              <MailIcon className="h-12 w-12 text-gray-300" />
              <h3 className="mt-2 text-lg font-medium text-gray-900 dark:text-white">
                No email selected
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Select an email to read it here
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Compose Email Modal */}
      {showCompose && (
        <ComposeEmail
          onClose={() => setShowCompose(false)}
          onSend={handleSendEmail}
        />
      )}
    </div>
  );
}

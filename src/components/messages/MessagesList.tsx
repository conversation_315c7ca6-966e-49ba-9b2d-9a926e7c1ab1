'use client';

import { useState } from 'react';
import { FiSearch, FiPlus, FiStar, FiPaperclip, FiRefreshCw, FiChevronLeft, FiChevronRight, FiMail, FiTrash2, FiArchive, FiClock, FiTag } from 'react-icons/fi';

interface Message {
  id: string;
  from: string;
  subject: string;
  body: string;
  time: string;
  read: boolean;
  starred: boolean;
  hasAttachment: boolean;
  avatar: string;
}

const messages: Message[] = [
  {
    id: '1',
    from: '<PERSON>',
    subject: 'Meeting Tomorrow',
    body: 'Hi there, just a reminder about our meeting tomorrow at 10 AM.',
    time: '10:30 AM',
    read: false,
    starred: true,
    hasAttachment: false,
    avatar: 'JD'
  },
  {
    id: '2',
    from: 'Acme Inc.',
    subject: 'Your Invoice #12345',
    body: 'Your invoice for the month of June is attached. Please review and let us know if you have any questions.',
    time: 'Yesterday',
    read: true,
    starred: false,
    hasAttachment: true,
    avatar: 'AI'
  },
  {
    id: '3',
    from: '<PERSON>',
    subject: 'Project Update: Q2 Marketing Campaign',
    body: 'Here is the latest update on the project we discussed during our last meeting.',
    time: 'Jun 18',
    read: true,
    starred: false,
    hasAttachment: false,
    avatar: 'SJ'
  },
  {
    id: '4',
    from: 'Team Standup',
    subject: 'Daily Standup Notes - June 20',
    body: 'Notes from today\'s standup meeting are now available in the shared drive.',
    time: 'Jun 17',
    read: true,
    starred: false,
    hasAttachment: true,
    avatar: 'TS'
  },
  {
    id: '5',
    from: 'HR Department',
    subject: 'New Company Policy: Remote Work Guidelines',
    body: 'Please review the updated company policy document regarding remote work arrangements.',
    time: 'Jun 15',
    read: true,
    starred: true,
    hasAttachment: false,
    avatar: 'HR'
  }
];

export function MessagesList() {
  const [selectedMessage, setSelectedMessage] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedMessages, setSelectedMessages] = useState<Set<string>>(new Set());

  const filteredMessages = messages.filter(message => 
    message.from.toLowerCase().includes(searchQuery.toLowerCase()) ||
    message.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
    message.body.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const toggleSelectMessage = (id: string) => {
    const newSelection = new Set(selectedMessages);
    if (newSelection.has(id)) {
      newSelection.delete(id);
    } else {
      newSelection.add(id);
    }
    setSelectedMessages(newSelection);
  };

  const toggleSelectAll = () => {
    if (selectedMessages.size === filteredMessages.length) {
      setSelectedMessages(new Set());
    } else {
      setSelectedMessages(new Set(filteredMessages.map(msg => msg.id)));
    }
  };

  const toggleStar = (id: string, e: React.MouseEvent) => {
    e.stopPropagation();
    // Toggle star logic would go here
  };

  return (
    <div className="flex flex-col h-full">
      {/* Toolbar */}
      <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 p-2 bg-white dark:bg-gray-800">
        <div className="flex items-center space-x-2">
          <button 
            onClick={toggleSelectAll}
            className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
            title="Select all"
          >
            <input 
              type="checkbox" 
              className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800" 
              checked={selectedMessages.size > 0 && selectedMessages.size === filteredMessages.length}
              onChange={() => {}}
            />
          </button>
          
          <button className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700" title="Refresh">
            <FiRefreshCw className="h-5 w-5 text-gray-500 dark:text-gray-400" />
          </button>
          
          {selectedMessages.size > 0 && (
            <div className="flex items-center space-x-1">
              <button className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700" title="Archive">
                <FiArchive className="h-5 w-5 text-gray-500 dark:text-gray-400" />
              </button>
              <button className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700" title="Delete">
                <FiTrash2 className="h-5 w-5 text-gray-500 dark:text-gray-400" />
              </button>
              <button className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700" title="Mark as unread">
                <FiMail className="h-5 w-5 text-gray-500 dark:text-gray-400" />
              </button>
              <button className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700" title="Snooze">
                <FiClock className="h-5 w-5 text-gray-500 dark:text-gray-400" />
              </button>
              <button className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700" title="Labels">
                <FiTag className="h-5 w-5 text-gray-500 dark:text-gray-400" />
              </button>
            </div>
          )}
        </div>
        
        <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
          <span>1-{filteredMessages.length} of {filteredMessages.length}</span>
          <button className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50" disabled>
            <FiChevronLeft className="h-5 w-5" />
          </button>
          <button className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50" disabled>
            <FiChevronRight className="h-5 w-5" />
          </button>
        </div>
      </div>
      
      {/* Search bar */}
      <div className="border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-2">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <FiSearch className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            className="block w-full rounded-lg border-0 bg-gray-100 py-2 pl-10 pr-4 text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 sm:text-sm dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
            placeholder="Search mail"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>
      
      {/* Message list */}
      <div className="flex-1 overflow-y-auto">
        {filteredMessages.map((message) => (
          <div 
            key={message.id} 
            className={`flex items-center border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer ${
              selectedMessages.has(message.id) ? 'bg-blue-50 dark:bg-gray-700' : 'bg-white dark:bg-gray-800'
            } ${!message.read ? 'font-semibold' : ''}`}
            onClick={() => toggleSelectMessage(message.id)}
          >
            <div className="flex items-center px-4 py-3 w-12">
              <input 
                type="checkbox" 
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
                checked={selectedMessages.has(message.id)}
                onChange={(e) => {
                  e.stopPropagation();
                  toggleSelectMessage(message.id);
                }}
              />
              <button 
                onClick={(e) => {
                  e.stopPropagation();
                  toggleStar(message.id, e);
                }}
                className="ml-2"
              >
                <FiStar 
                  className={`h-5 w-5 ${message.starred ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300 hover:text-gray-400'}`} 
                />
              </button>
            </div>
            
            <div className="flex-1 min-w-0 px-2 py-3">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-800 dark:text-blue-200 font-medium text-sm mr-3 flex-shrink-0">
                  {message.avatar}
                </div>
                <div className="min-w-0 flex-1">
                  <div className="flex items-center justify-between">
                    <p className={`text-sm truncate ${!message.read ? 'text-gray-900 dark:text-white' : 'text-gray-600 dark:text-gray-300'}`}>
                      {message.from}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 ml-2 whitespace-nowrap">
                      {message.time}
                    </p>
                  </div>
                  <p className={`text-sm truncate ${!message.read ? 'text-gray-900 dark:text-white' : 'text-gray-600 dark:text-gray-400'}`}>
                    {message.subject}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                    {message.body}
                  </p>
                </div>
                {message.hasAttachment && (
                  <FiPaperclip className="h-4 w-4 text-gray-400 ml-2 flex-shrink-0" />
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

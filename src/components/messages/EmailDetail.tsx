import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { FiArrowLeft, FiArchive, FiClock, FiCornerUpLeft, FiCornerUpRight, FiMoreVertical, FiPrinter, FiTrash2, FiInbox } from "react-icons/fi";

export function EmailDetail({
  email,
  onBack,
}: {
  email: {
    id: string;
    from: string;
    to: string[];
    subject: string;
    date: Date;
    body: string;
    isRead: boolean;
    hasAttachment: boolean;
  } | null;
  onBack: () => void;
}) {
  if (!email) {
    return (
      <div className="flex h-full flex-col items-center justify-center p-8 text-center">
        <div className="rounded-full bg-gray-100 p-4 dark:bg-gray-800">
          <FiInbox className="h-12 w-12 text-gray-400" />
        </div>
        <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-white">
          No email selected
        </h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Select an email to read it here
        </p>
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col">
      <div className="flex items-center justify-between border-b border-gray-200 p-4 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="icon" onClick={onBack} className="md:hidden">
            <FiArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex space-x-2">
            <Button variant="ghost" size="icon">
              <FiArchive className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon">
              <FiTrash2 className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon">
              <FiPrinter className="h-5 w-5" />
            </Button>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="ghost" size="icon">
            <FiClock className="h-5 w-5" />
          </Button>
          <Button variant="ghost" size="icon">
            <FiMoreVertical className="h-5 w-5" />
          </Button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-4">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {email.subject}
          </h1>
          <div className="mt-4 flex items-start">
            <Avatar className="h-10 w-10">
              <AvatarImage src={`https://i.pravatar.cc/150?u=${email.from}`} />
              <AvatarFallback>{email.from.charAt(0)}</AvatarFallback>
            </Avatar>
            <div className="ml-4">
              <div className="flex items-center">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {email.from}
                </p>
                <span className="mx-2 text-gray-400">•</span>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {new Date(email.date).toLocaleString()}
                </p>
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                to {email.to.join(", ")}
              </p>
            </div>
          </div>
        </div>

        <div className="prose max-w-none dark:prose-invert">
          <p className="whitespace-pre-line text-gray-800 dark:text-gray-200">
            {email.body}
          </p>
        </div>

        {email.hasAttachment && (
          <div className="mt-6 rounded-lg border border-gray-200 p-4 dark:border-gray-700">
            <h3 className="mb-2 text-sm font-medium text-gray-900 dark:text-white">
              Attachments (1)
            </h3>
            <div className="flex items-center rounded-lg border border-gray-200 p-3 dark:border-gray-700">
              <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded bg-blue-50 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400">
                <svg
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  Document.pdf
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  2.4 MB • PDF Document
                </p>
              </div>
              <Button variant="ghost" size="sm">
                Download
              </Button>
            </div>
          </div>
        )}
      </div>

      <div className="border-t border-gray-200 p-4 dark:border-gray-700">
        <div className="flex justify-between">
          <Button variant="outline" size="sm">
            <FiCornerUpLeft className="mr-2 h-4 w-4" />
            Reply
          </Button>
          <Button variant="outline" size="sm">
            <FiCornerUpRight className="mr-2 h-4 w-4" />
            Forward
          </Button>
        </div>
      </div>
    </div>
  );
}

import { cn } from "@/lib/utils";
import { formatDistanceToNow } from "date-fns";
import { FiStar, FiMail, FiPaperclip } from "react-icons/fi";

interface EmailListItemProps {
  id: string;
  from: string;
  subject: string;
  preview: string;
  time: Date;
  isRead: boolean;
  isStarred: boolean;
  hasAttachment: boolean;
  isSelected: boolean;
  onClick: () => void;
}

export function EmailListItem({
  id,
  from,
  subject,
  preview,
  time,
  isRead,
  isStarred,
  hasAttachment,
  isSelected,
  onClick,
}: EmailListItemProps) {
  return (
    <div
      className={cn(
        "flex cursor-pointer items-start border-b border-gray-200 p-3 hover:bg-gray-100 dark:border-gray-700 dark:hover:bg-gray-800",
        isSelected && "bg-blue-50 dark:bg-gray-800",
        isRead ? "bg-white dark:bg-gray-900" : "bg-blue-50 font-medium dark:bg-gray-900"
      )}
      onClick={onClick}
    >
      <div className="mr-3 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-300">
        {from.charAt(0).toUpperCase()}
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <p className="truncate text-sm font-medium text-gray-900 dark:text-white">
            {from}
          </p>
          <div className="ml-2 flex items-center text-xs text-gray-500 dark:text-gray-400">
            {formatDistanceToNow(new Date(time), { addSuffix: true })}
            <button
              className="ml-2 text-gray-400 hover:text-yellow-500 dark:hover:text-yellow-400"
              onClick={(e: React.MouseEvent) => {
                e.stopPropagation();
                // Toggle star
              }}
            >
              <FiStar
                className={cn(
                  "h-4 w-4",
                  isStarred && "fill-yellow-400 text-yellow-400"
                )}
              />
            </button>
          </div>
        </div>
        <p className="truncate text-sm font-medium text-gray-900 dark:text-white">
          {subject}
        </p>
        <p className="truncate text-sm text-gray-500 dark:text-gray-400">
          {preview}
        </p>
        <div className="mt-1 flex items-center">
          {hasAttachment && (
            <FiPaperclip className="mr-1 h-3 w-3 text-gray-400" />
          )}
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {hasAttachment ? "1 attachment" : ""}
          </span>
        </div>
      </div>
      {!isRead && (
        <div className="ml-2 h-2 w-2 flex-shrink-0 rounded-full bg-blue-500"></div>
      )}
    </div>
  );
}

import React from 'react';
import Image from 'next/image';

interface InvitationTemplateProps {
  recipientName?: string;
  companyName?: string;
  invitationLink: string;
  expiryDate?: string;
  senderName?: string;
  senderTitle?: string;
}

const InvitationTemplate: React.FC<InvitationTemplateProps> = ({
  recipientName = '[Recipient Name]',
  companyName = '[Company Name]',
  invitationLink,
  expiryDate = '30 days',
  senderName = 'Ascension Team',
  senderTitle = 'Procurement Manager',
}) => {
  return (
    <div style={{ fontFamily: 'Arial, sans-serif', maxWidth: '600px', margin: '0 auto' }}>
      {/* Header */}
      <div style={{ backgroundColor: '#f8f9fa', padding: '20px', textAlign: 'center', borderBottom: '2px solid #18546c' }}>
        <Image 
          src="https://placeholder.com/wp-content/uploads/2018/10/placeholder.com-logo1.png" 
          alt="Ascension Logo" 
          width={200}
          height={50}
          style={{ height: '50px', marginBottom: '10px' }}
        />
        <h1 style={{ color: '#18546c', fontSize: '24px', margin: '0' }}>Invitation to Join Ascension Supplier Network</h1>
      </div>

      {/* Body */}
      <div style={{ padding: '30px 20px', backgroundColor: '#ffffff' }}>
        <p style={{ fontSize: '16px', lineHeight: '1.5', color: '#333' }}>Dear {recipientName},</p>
        
        <p style={{ fontSize: '16px', lineHeight: '1.5', color: '#333' }}>
          You have been invited to join the Ascension Supplier Network as a representative of <strong>{companyName}</strong>.
        </p>
        
        <p style={{ fontSize: '16px', lineHeight: '1.5', color: '#333' }}>
          Ascension is a leading procurement platform that connects businesses with quality suppliers. By joining our network, you&apos;ll gain access to:
        </p>
        
        <ul style={{ fontSize: '16px', lineHeight: '1.5', color: '#333' }}>
          <li>New business opportunities</li>
          <li>Streamlined procurement processes</li>
          <li>Efficient communication with clients</li>
          <li>Simplified invoicing and payment tracking</li>
        </ul>
        
        <p style={{ fontSize: '16px', lineHeight: '1.5', color: '#333' }}>
          To complete your registration, please click the button below:
        </p>
        
        <div style={{ textAlign: 'center', margin: '30px 0' }}>
          <a 
            href={invitationLink} 
            style={{
              backgroundColor: '#18546c',
              color: '#ffffff',
              padding: '12px 24px',
              textDecoration: 'none',
              borderRadius: '4px',
              fontWeight: 'bold',
              display: 'inline-block',
            }}
          >
            Accept Invitation
          </a>
        </div>
        
        <p style={{ fontSize: '16px', lineHeight: '1.5', color: '#333' }}>
          This invitation will expire in {expiryDate}. If you have any questions, please don&apos;t hesitate to contact <NAME_EMAIL>.
        </p>
        
        <p style={{ fontSize: '16px', lineHeight: '1.5', color: '#333', marginTop: '30px' }}>
          Best regards,
        </p>
        
        <p style={{ fontSize: '16px', lineHeight: '1.5', color: '#333', margin: '0' }}>
          {senderName}
        </p>
        
        <p style={{ fontSize: '16px', lineHeight: '1.5', color: '#333', margin: '0' }}>
          {senderTitle}
        </p>
        
        <p style={{ fontSize: '16px', lineHeight: '1.5', color: '#333', margin: '0' }}>
          Ascension
        </p>
      </div>

      {/* Footer */}
      <div style={{ backgroundColor: '#f8f9fa', padding: '20px', textAlign: 'center', borderTop: '1px solid #dee2e6' }}>
        <p style={{ fontSize: '14px', color: '#6c757d', margin: '0 0 10px 0' }}>
          © {new Date().getFullYear()} Ascension. All rights reserved.
        </p>
        <p style={{ fontSize: '12px', color: '#6c757d', margin: '0' }}>
          This message was sent to you by Ascension. Intuitive purchasing.
        </p>
      </div>
    </div>
  );
};

export default InvitationTemplate;

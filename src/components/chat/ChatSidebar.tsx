"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { FiChevronDown, FiMessageSquare, FiSearch, FiUser, FiUsers } from "react-icons/fi";
import { Conversation, User } from "./types";

interface ChatSidebarProps {
  conversations: Conversation[];
  activeConversation: string | null;
  currentUser?: User;
  onSelectConversation: (id: string) => void;
  onNewChat: () => void;
}

export function ChatSidebar({
  conversations,
  activeConversation,
  onSelectConversation,
  onNewChat,
  currentUser = { id: 'current', name: 'You' },
}: ChatSidebarProps) {
  return (
    <div className="flex h-full w-80 flex-col border-r border-gray-200 bg-white dark:border-gray-800 dark:bg-gray-900">
      <div className="p-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold">Messages</h2>
          <div className="flex items-center space-x-2">
            <button className="rounded-full p-1 hover:bg-gray-100 dark:hover:bg-gray-800">
              <FiMessageSquare className="h-5 w-5" />
            </button>
            <button className="rounded-full p-1 hover:bg-gray-100 dark:hover:bg-gray-800">
              <FiUsers className="h-5 w-5" />
            </button>
          </div>
        </div>
        <div className="mt-4">
          <div className="relative">
            <FiSearch className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              placeholder="Search messages"
              className="w-full pl-9"
            />
          </div>
        </div>
      </div>
      
      <div className="flex-1 overflow-y-auto">
        <div className="space-y-1 px-2">
          {conversations.map((conversation) => (
            <div
              key={conversation.id}
              className={cn(
                "flex cursor-pointer items-center space-x-3 rounded-lg p-3 hover:bg-gray-100 dark:hover:bg-gray-800",
                activeConversation === conversation.id && "bg-gray-100 dark:bg-gray-800"
              )}
              onClick={() => onSelectConversation(conversation.id)}
            >
              <div className="relative">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={conversation.avatar} alt={conversation.name} />
                  <AvatarFallback>
                    {conversation.name
                      .split(" ")
                      .map((n: string) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                {conversation.isOnline && (
                  <div className="absolute bottom-0 right-0 h-2.5 w-2.5 rounded-full border-2 border-white bg-green-500 dark:border-gray-900" />
                )}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h3 className="truncate font-medium">{conversation.name}</h3>
                  <span className="text-xs text-gray-500">
                    {conversation.lastMessageTime}
                  </span>
                </div>
                <p className="truncate text-sm text-gray-500">
                  {conversation.lastMessage}
                </p>
              </div>
              {conversation.unreadCount > 0 && (
                <div className="flex h-5 w-5 items-center justify-center rounded-full bg-blue-600 text-xs font-medium text-white">
                  {conversation.unreadCount}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
      
      <div className="border-t border-gray-200 p-4 dark:border-gray-800">
        <button
          onClick={onNewChat}
          className="flex w-full items-center justify-center rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700"
        >
          New Message
        </button>
      </div>
    </div>
  );
}

"use client";

import { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { 
  FiPaperclip, 
  FiSend, 
  FiChevronDown, 
  FiSearch, 
  FiMoreVertical,
  FiPhone,
  FiVideo,
  FiMic
} from "react-icons/fi";
import { Message, User } from "./types";

const getStatusIndicator = (status: Message['status']) => {
  switch (status) {
    case 'sending':
      return <span className="text-gray-400">Sending...</span>;
    case 'sent':
      return <span className="text-gray-400">✓</span>;
    case 'delivered':
      return <span className="text-gray-400">✓✓</span>;
    case 'read':
      return <span className="text-blue-500">✓✓</span>;
    default:
      return null;
  }
};

interface ChatInterfaceProps {
  messages: Message[];
  onSendMessage: (message: string) => void;
  currentUser: User;
  title?: string;
  subtitle?: string;
  showHeader?: boolean;
  onBack?: () => void;
  isMobile?: boolean;
}

export function ChatInterface({
  messages,
  onSendMessage,
  currentUser,
  title = "Chat",
  subtitle = "Online",
  showHeader = true,
  onBack,
  isMobile = false,
}: ChatInterfaceProps) {
  const [newMessage, setNewMessage] = useState("");

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (newMessage.trim()) {
      onSendMessage(newMessage);
      setNewMessage("");
    }
  };

  return (
    <div className="flex h-full flex-col bg-white dark:bg-gray-900">
      {showHeader && (
        <div className="flex items-center justify-between border-b border-gray-200 p-4 dark:border-gray-800">
          <div className="flex items-center space-x-3">
            {isMobile && onBack && (
              <Button 
                variant="ghost" 
                size="icon" 
                className="md:hidden"
                onClick={onBack}
              >
                <FiChevronDown className="h-5 w-5 rotate-90" />
              </Button>
            )}
            <Avatar className="h-10 w-10">
              <AvatarImage src={currentUser.avatar} alt={currentUser.name} />
              <AvatarFallback>
                {currentUser.name
                  .split(" ")
                  .map((n) => n[0])
                  .join("")}
              </AvatarFallback>
            </Avatar>
            <div>
              <h2 className="text-base font-semibold text-gray-900 dark:text-white">{title}</h2>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {subtitle}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-1">
            <Button variant="ghost" size="icon" className="h-9 w-9">
              <FiPhone className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" className="h-9 w-9">
              <FiVideo className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" className="h-9 w-9">
              <FiSearch className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" className="h-9 w-9">
              <FiMoreVertical className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {messages.map((message, index) => {
            const showHeader = index === 0 || 
              messages[index - 1].senderId !== message.senderId ||
              new Date(message.time).getTime() - new Date(messages[index - 1].time).getTime() > 30 * 60 * 1000; // 30 minutes

            return (
              <div
                key={message.id}
                className={cn(
                  "group relative flex items-start gap-3 px-2",
                  message.isCurrentUser ? "justify-end" : "justify-start"
                )}
              >
                {!message.isCurrentUser && showHeader && (
                  <Avatar className="h-8 w-8">
                    {message.avatar ? (
                      <AvatarImage src={message.avatar} alt={message.sender} />
                    ) : (
                      <AvatarFallback className="text-xs">
                        {message.sender
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    )}
                  </Avatar>
                )}
                {!message.isCurrentUser && !showHeader && (
                  <div className="w-8"></div> // Spacer for alignment
                )}
                <div
                  className={cn(
                    "group relative max-w-[80%] rounded-2xl px-4 py-2",
                    message.isCurrentUser
                      ? "rounded-tr-none bg-blue-100 text-gray-900 dark:bg-blue-900 dark:text-white"
                      : "rounded-tl-none bg-gray-100 dark:bg-gray-800"
                  )}
                >
                  {!message.isCurrentUser && showHeader && (
                    <div className="text-xs font-medium text-blue-600 dark:text-blue-400">
                      {message.sender}
                    </div>
                  )}
                  <p className="text-sm">{message.content}</p>
                  <div
                    className={cn(
                      "mt-0.5 flex items-center justify-end gap-1 text-xs",
                      message.isCurrentUser ? "text-blue-600 dark:text-blue-300" : "text-gray-500"
                    )}
                  >
                    <span>{message.time}</span>
                    {message.isCurrentUser && (
                      <span className="ml-1">
                        {getStatusIndicator(message.status)}
                      </span>
                    )}
                  </div>
                </div>
                {message.isCurrentUser && (
                  <div className="absolute -right-8 top-1/2 hidden -translate-y-1/2 transform group-hover:block">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-7 w-7 rounded-full bg-gray-100 text-gray-500 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700"
                    >
                      <FiMoreVertical className="h-3.5 w-3.5" />
                    </Button>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </ScrollArea>

      <div className="border-t border-gray-200 p-3 dark:border-gray-800">
        <form 
          onSubmit={handleSendMessage} 
          className="flex items-center gap-2 rounded-full border border-gray-200 bg-gray-50 px-4 py-1.5 dark:border-gray-700 dark:bg-gray-800"
        >
          <Button 
            type="button" 
            variant="ghost" 
            size="icon" 
            className="h-9 w-9 rounded-full text-gray-500 hover:bg-transparent hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            <FiPaperclip className="h-5 w-5" />
          </Button>
          <Input
            type="text"
            placeholder="Type a message..."
            className="flex-1 border-0 bg-transparent px-0 shadow-none focus-visible:ring-0"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
          />
          <div className="flex items-center">
            <Button 
              type="button" 
              variant="ghost" 
              size="icon" 
              className="h-9 w-9 rounded-full text-gray-500 hover:bg-transparent hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            >
              <FiMic className="h-5 w-5" />
            </Button>
            <Button 
              type="submit" 
              variant="ghost"
              size="icon"
              className="h-9 w-9 rounded-full text-blue-600 hover:bg-blue-100 hover:text-blue-700 dark:text-blue-400 dark:hover:bg-blue-900/50 dark:hover:text-blue-300"
              disabled={!newMessage.trim()}
            >
              <FiSend className="h-5 w-5" />
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}

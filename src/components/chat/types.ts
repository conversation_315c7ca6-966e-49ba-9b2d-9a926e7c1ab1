export interface Message {
  id: string;
  sender: string;
  senderId: string;
  content: string;
  time: string;
  isCurrentUser: boolean;
  avatar?: string;
  status: 'sending' | 'sent' | 'delivered' | 'read';
  isEdited?: boolean;
}

export interface Conversation {
  id: string;
  name: string;
  avatar?: string;
  lastMessage: string;
  lastMessageTime: string;
  unreadCount: number;
  isOnline: boolean;
  messages: Message[];
}

export interface User {
  id: string;
  name: string;
  avatar?: string;
  status?: 'online' | 'offline' | 'away';
  lastSeen?: string;
  role?: string;
}

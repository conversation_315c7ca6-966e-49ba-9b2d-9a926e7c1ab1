"use client";

import { useState } from "react";
import { ChatInterface } from "./ChatInterface";
import { ChatSidebar } from "./ChatSidebar";
import { Message, Conversation } from "./types";

export function ChatContainer() {
  const [conversations, setConversations] = useState<Conversation[]>([
    {
      id: "1",
      name: "<PERSON>",
      avatar: "https://randomuser.me/api/portraits/men/1.jpg",
      lastMessage: "Thanks for your message! Let me check that for you.",
      lastMessageTime: "10:15 AM",
      unreadCount: 2,
      isOnline: true,
      messages: [
        {
          id: "1",
          sender: "<PERSON>",
          senderId: "1",
          content: "Hi there! How can I help you today?",
          time: "10:00 AM",
          isCurrentUser: false,
          avatar: "https://randomuser.me/api/portraits/men/1.jpg",
          status: "read",
        },
        {
          id: "2",
          sender: "You",
          senderId: "current",
          content: "Hi! I have a question about my order.",
          time: "10:02 AM",
          isCurrentUser: true,
          status: "read",
        },
        {
          id: "3",
          sender: "<PERSON>",
          senderId: "1",
          content: "Sure, I'd be happy to help. What's your order number?",
          time: "10:03 AM",
          isCurrentUser: false,
          avatar: "https://randomuser.me/api/portraits/men/1.jpg",
          status: "read",
        },
        {
          id: "4",
          sender: "You",
          senderId: "current",
          content: "It's #12345. I was wondering about the delivery status.",
          time: "10:05 AM",
          isCurrentUser: true,
          status: "read",
        },
      ],
    },
    // Add more conversations here
  ]);

  const [activeConversation, setActiveConversation] = useState<string | null>("1");
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  const currentConversation = conversations.find(c => c.id === activeConversation);

  const handleSendMessage = (content: string) => {
    if (!activeConversation) return;
    
    const newMessage: Message = {
      id: Date.now().toString(),
      sender: "You",
      senderId: "current",
      content,
      time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      isCurrentUser: true,
      status: 'sent',
    };

    setConversations(prev => 
      prev.map(conv => 
        conv.id === activeConversation
          ? { ...conv, messages: [...conv.messages, newMessage] }
          : conv
      )
    );
  };

  const handleSelectConversation = (id: string) => {
    setActiveConversation(id);
    setIsMobileSidebarOpen(false);
  };

  const handleNewChat = () => {
    // Implement new chat logic here
    console.log("New chat clicked");
  };

  return (
    <div className="flex h-screen bg-white dark:bg-gray-900">
      {/* Sidebar */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-20 w-80 transform border-r border-gray-200 bg-white transition-transform duration-300 ease-in-out dark:border-gray-800 dark:bg-gray-900 md:relative md:translate-x-0",
        isMobileSidebarOpen ? "translate-x-0" : "-translate-x-full"
      )}>
        <ChatSidebar
          conversations={conversations}
          activeConversation={activeConversation}
          onSelectConversation={handleSelectConversation}
          onNewChat={handleNewChat}
        />
      </div>

      {/* Overlay */}
      {isMobileSidebarOpen && (
        <div 
          className="fixed inset-0 z-10 bg-black/50 md:hidden"
          onClick={() => setIsMobileSidebarOpen(false)}
        />
      )}

      {/* Main Chat Area */}
      <div className="flex flex-1 flex-col">
        {currentConversation ? (
          <ChatInterface
            messages={currentConversation.messages}
            onSendMessage={handleSendMessage}
            currentUser={{
              id: "current",
              name: "You",
              status: "online"
            }}
            title={currentConversation.name}
            subtitle={currentConversation.isOnline ? "Online" : "Offline"}
            onBack={() => setIsMobileSidebarOpen(true)}
            isMobile={true}
          />
        ) : (
          <div className="flex h-full flex-col items-center justify-center p-4 text-center">
            <div className="max-w-md">
              <h2 className="mb-2 text-xl font-semibold text-gray-800 dark:text-white">
                No conversation selected
              </h2>
              <p className="text-gray-500 dark:text-gray-400">
                Select a conversation from the sidebar or start a new one.
              </p>
              <button
                onClick={() => setIsMobileSidebarOpen(true)}
                className="mt-4 rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 md:hidden"
              >
                Open conversations
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

function cn(...classes: string[]) {
  return classes.filter(Boolean).join(" ");
}

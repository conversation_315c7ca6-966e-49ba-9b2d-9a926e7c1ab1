'use client';

import { LoadingSpinner } from '@/components/ui/loading-spinner';

interface LoadingProps {
  fullScreen?: boolean;
  text?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function Loading({ 
  fullScreen = true, 
  text = 'Loading...',
  size = 'md',
  className = ''
}: LoadingProps) {
  return (
    <div className={`flex items-center justify-center ${fullScreen ? 'min-h-screen' : 'py-8'} ${className}`}>
      <LoadingSpinner 
        size={size}
        text={text}
        fullScreen={fullScreen}
      />
    </div>
  );
}

// Full page loading component
export function FullPageLoading() {
  return <Loading fullScreen text="Loading..." size="lg" />;
}

// Inline loading component
export function InlineLoading() {
  return <Loading fullScreen={false} text="" size="sm" className="py-2" />;
}

// Button loading component
export function ButtonLoading() {
  return (
    <div className="flex items-center justify-center">
      <LoadingSpinner size="sm" />
    </div>
  );
}

import { cn } from "@/lib/utils"

interface LoadingSpinnerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: 'sm' | 'md' | 'lg'
  text?: string
  fullScreen?: boolean
}

export function LoadingSpinner({
  className,
  size = 'md',
  text,
  fullScreen = false,
  ...props
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4 border-2',
    md: 'h-8 w-8 border-2',
    lg: 'h-12 w-12 border-4',
  }

  return (
    <div
      className={cn(
        'flex flex-col items-center justify-center',
        fullScreen ? 'min-h-screen' : '',
        className
      )}
      {...props}
    >
      <div className="relative">
        <div
          className={cn(
            'animate-spin rounded-full border-solid border-blue-500 border-t-transparent',
            sizeClasses[size]
          )}
          style={{
            animationDuration: '1s',
            animationTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)',
          }}
          role="status"
        >
          <span className="sr-only">Loading...</span>
        </div>
        <div
          className={cn(
            'absolute inset-0 flex items-center justify-center',
            size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : 'text-base'
          )}
        >
          {size !== 'sm' && (
            <svg
              className="animate-pulse text-blue-500"
              fill="currentColor"
              viewBox="0 0 24 24"
              width={size === 'lg' ? 24 : 16}
              height={size === 'lg' ? 24 : 16}
            >
              <path d="M12 2a10 10 0 100 20 10 10 0 000-20zm0 18a8 8 0 110-16 8 8 0 010 16z" />
            </svg>
          )}
        </div>
      </div>
      {text && (
        <p className="mt-3 text-sm font-medium text-gray-500 dark:text-gray-400">
          {text}
        </p>
      )}
    </div>
  )
}

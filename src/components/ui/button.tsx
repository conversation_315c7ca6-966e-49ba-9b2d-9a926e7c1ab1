import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-gray-100 text-[#18546c] hover:bg-[#18546c] hover:text-white dark:bg-slate-800 dark:text-white dark:hover:bg-[#18546c] shadow hover:shadow-md transition-colors duration-200",
        destructive:
          "bg-red-100 text-red-700 hover:bg-red-700 hover:text-white dark:bg-red-900/20 dark:text-red-400 dark:hover:bg-red-900 dark:hover:text-white shadow hover:shadow-md transition-colors duration-200",
        outline:
          "border border-[#18546c] bg-transparent text-[#18546c] hover:bg-[#18546c] hover:text-white dark:border-slate-500 dark:text-slate-300 dark:hover:bg-[#18546c] dark:hover:text-white shadow hover:shadow-md transition-colors duration-200",
        secondary:
          "bg-slate-200 text-slate-700 hover:bg-slate-700 hover:text-white dark:bg-slate-700 dark:text-slate-300 dark:hover:bg-slate-600 dark:hover:text-white shadow hover:shadow-md transition-colors duration-200",
        ghost: "text-[#18546c] hover:bg-[#18546c]/10 dark:text-slate-300 dark:hover:bg-slate-800 dark:hover:text-white transition-colors duration-200",
        link: "text-[#18546c] underline-offset-4 hover:underline dark:text-slate-300 transition-colors duration-200",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }

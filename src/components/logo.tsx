import darkLogo from "@/assets/logos/dark.png";
import lightLogo from "@/assets/logos/light.png";
import Image from "next/image";
import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';

interface LogoProps {
  variant?: 'light' | 'dark';
}

export function Logo({ variant }: LogoProps) {
  const { theme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  
  // After mounting, we have access to the theme
  useEffect(() => setMounted(true), []);
  
  // Use a default logo for server-side rendering to avoid hydration mismatch
  if (!mounted) {
    return (
      <div className="relative h-12 max-w-[12.847rem]">
        <Image
          src={lightLogo} // Default to light logo for SSR
          alt="Ascension"
          role="presentation"
          quality={100}
          priority
        />
      </div>
    );
  }
  
  // Once mounted on client, we can use the theme
  const displayVariant = variant || resolvedTheme || 'light';

  return (
    <div className="relative h-12 max-w-[12.847rem]">
      {displayVariant === 'light' ? (
        <Image
          src={lightLogo}
          alt="Ascension"
          role="presentation"
          quality={100}
          priority
        />
      ) : (
        <Image
          src={darkLogo}
          alt="Ascension"
          role="presentation"
          quality={100}
          priority
        />
      )}
    </div>
  );
}

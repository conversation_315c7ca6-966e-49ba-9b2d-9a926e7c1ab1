import React, { useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { Loading } from '@/components/global/Loading';
import { FiChevronDown, FiChevronUp, FiMoreVertical, FiExternalLink } from 'react-icons/fi';
import Link from 'next/link';

interface Inquiry {
  id: string;
  client: string;
  year: number;
  caseNo: string;
  reference: string;
  subject: string;
  status: 'Pending' | 'In Progress' | 'Completed' | 'On Hold';
  dateRequested: string;
  deliveryRequired: string;
}

const mockInquiries: Inquiry[] = [
  {
    id: '1',
    client: 'Acme Corporation',
    year: 2023,
    caseNo: 'C-2023-045',
    reference: 'INQ-045-23',
    subject: 'Annual Financial Audit',
    status: 'Pending',
    dateRequested: '2023-06-15',
    deliveryRequired: '2023-07-15',
  },
  {
    id: '2',
    client: 'Globex Industries',
    year: 2023,
    caseNo: 'C-2023-129',
    reference: 'INQ-129-23',
    subject: 'Q2 Financial Review',
    status: 'Pending',
    dateRequested: '2023-06-18',
    deliveryRequired: '2023-07-05',
  },
  {
    id: '3',
    client: 'Stark Enterprises',
    year: 2023,
    caseNo: 'C-2023-156',
    reference: 'INQ-156-23',
    subject: 'Tax Compliance Review',
    status: 'Pending',
    dateRequested: '2023-06-20',
    deliveryRequired: '2023-07-10',
  },
  {
    id: '4',
    client: 'Wayne Industries',
    year: 2023,
    caseNo: 'C-2023-178',
    reference: 'INQ-178-23',
    subject: 'Annual Compliance Report',
    status: 'Pending',
    dateRequested: '2023-06-22',
    deliveryRequired: '2023-07-08',
  },
  {
    id: '5',
    client: 'Umbrella Corp',
    year: 2023,
    caseNo: 'C-2023-201',
    reference: 'INQ-201-23',
    subject: 'Internal Controls Assessment',
    status: 'Pending',
    dateRequested: '2023-06-25',
    deliveryRequired: '2023-07-12',
  },
  {
    id: '6',
    client: 'Cyberdyne Systems',
    year: 2023,
    caseNo: 'C-2023-215',
    reference: 'INQ-215-23',
    subject: 'IT Security Audit',
    status: 'Pending',
    dateRequested: '2023-06-27',
    deliveryRequired: '2023-07-20',
  },
  {
    id: '7',
    client: 'InGen Corporation',
    year: 2023,
    caseNo: 'C-2023-231',
    reference: 'INQ-231-23',
    subject: 'Research & Development Review',
    status: 'Pending',
    dateRequested: '2023-06-28',
    deliveryRequired: '2023-07-18',
  },
  {
    id: '8',
    client: 'Oscorp Industries',
    year: 2023,
    caseNo: 'C-2023-245',
    reference: 'INQ-245-23',
    subject: 'Q3 Financial Forecast',
    status: 'Pending',
    dateRequested: '2023-06-29',
    deliveryRequired: '2023-07-22',
  },
  {
    id: '9',
    client: 'Stark Industries',
    year: 2023,
    caseNo: 'C-2023-267',
    reference: 'INQ-267-23',
    subject: 'Quarterly Tax Filing',
    status: 'Pending',
    dateRequested: '2023-06-30',
    deliveryRequired: '2023-07-25',
  },
  {
    id: '10',
    client: 'Wayne Enterprises',
    year: 2023,
    caseNo: 'C-2023-289',
    reference: 'INQ-289-23',
    subject: 'Annual Shareholder Report',
    status: 'Pending',
    dateRequested: '2023-07-01',
    deliveryRequired: '2023-07-28',
  },
  {
    id: '11',
    client: 'LexCorp',
    year: 2023,
    caseNo: 'C-2023-301',
    reference: 'INQ-301-23',
    subject: 'Merger Due Diligence',
    status: 'Pending',
    dateRequested: '2023-07-03',
    deliveryRequired: '2023-07-30',
  },
  {
    id: '12',
    client: 'Queen Consolidated',
    year: 2023,
    caseNo: 'C-2023-315',
    reference: 'INQ-315-23',
    subject: 'Financial Statement Review',
    status: 'Pending',
    dateRequested: '2023-07-05',
    deliveryRequired: '2023-08-01',
  },
  {
    id: '13',
    client: 'Parker Industries',
    year: 2023,
    caseNo: 'C-2023-328',
    reference: 'INQ-328-23',
    subject: 'Tax Planning Session',
    status: 'Pending',
    dateRequested: '2023-07-07',
    deliveryRequired: '2023-08-05',
  },
  {
    id: '14',
    client: 'Rand Enterprises',
    year: 2023,
    caseNo: 'C-2023-342',
    reference: 'INQ-342-23',
    subject: 'Annual Audit Preparation',
    status: 'Pending',
    dateRequested: '2023-07-08',
    deliveryRequired: '2023-08-08',
  },
  {
    id: '15',
    client: 'Virtucon',
    year: 2023,
    caseNo: 'C-2023-356',
    reference: 'INQ-356-23',
    subject: 'Internal Audit Review',
    status: 'Pending',
    dateRequested: '2023-07-10',
    deliveryRequired: '2023-08-12',
  },
  {
    id: '16',
    client: 'Stark Resorts',
    year: 2023,
    caseNo: 'C-2023-372',
    reference: 'INQ-372-23',
    subject: 'Financial Compliance Check',
    status: 'Pending',
    dateRequested: '2023-07-12',
    deliveryRequired: '2023-08-15',
  },
  {
    id: '17',
    client: 'Wayne Tech',
    year: 2023,
    caseNo: 'C-2023-389',
    reference: 'INQ-389-23',
    subject: 'Quarterly Financial Review',
    status: 'Pending',
    dateRequested: '2023-07-15',
    deliveryRequired: '2023-08-18',
  },
  {
    id: '18',
    client: 'Queen Industries',
    year: 2023,
    caseNo: 'C-2023-401',
    reference: 'INQ-401-23',
    subject: 'Annual Tax Return',
    status: 'Pending',
    dateRequested: '2023-07-18',
    deliveryRequired: '2023-08-22',
  },
  {
    id: '19',
    client: 'Pym Technologies',
    year: 2023,
    caseNo: 'C-2023-415',
    reference: 'INQ-415-23',
    subject: 'Financial Planning Session',
    status: 'Pending',
    dateRequested: '2023-07-20',
    deliveryRequired: '2023-08-25',
  },
  {
    id: '20',
    client: 'Stark-Fujikawa',
    year: 2023,
    caseNo: 'C-2023-427',
    reference: 'INQ-427-23',
    subject: 'Merger Documentation Review',
    status: 'Pending',
    dateRequested: '2023-07-22',
    deliveryRequired: '2023-08-28',
  }
];

interface InquiriesTableProps {
  searchTerm?: string;
  isLoading?: boolean;
}

export default function InquiriesTable({ searchTerm = '', isLoading = false }: InquiriesTableProps) {
  const [sortConfig, setSortConfig] = useState<{ key: keyof Inquiry; direction: 'asc' | 'desc' } | null>(null);
  const [inquiries] = useState<Inquiry[]>(mockInquiries);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Filter and sort inquiries
  const filteredAndSortedInquiries = React.useMemo(() => {
    // Filter first
    let result = [...inquiries];
    
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase();
      result = result.filter(inquiry => 
        Object.values(inquiry).some(
          value => value && value.toString().toLowerCase().includes(term)
        )
      );
    }
    
    // Then sort if sortConfig exists
    if (sortConfig) {
      result.sort((a, b) => {
        const aValue = a[sortConfig.key];
        const bValue = b[sortConfig.key];
        
        // Handle different data types for sorting
        if (aValue < bValue) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }
    
    return result;
  }, [inquiries, searchTerm, sortConfig]);

  // Get current inquiries for pagination
  const indexOfLastInquiry = currentPage * itemsPerPage;
  const indexOfFirstInquiry = indexOfLastInquiry - itemsPerPage;
  const currentInquiries = filteredAndSortedInquiries.slice(indexOfFirstInquiry, indexOfLastInquiry);
  const totalPages = Math.ceil(filteredAndSortedInquiries.length / itemsPerPage);
  
  // Reset to first page when search term changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Change page
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  // Handle items per page change
  const handleItemsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  const requestSort = (key: keyof Inquiry) => {
    setCurrentPage(1); // Reset to first page when changing sort
    
    // Toggle sort direction if same column is clicked
    if (sortConfig && sortConfig.key === key) {
      setSortConfig({
        key,
        direction: sortConfig.direction === 'asc' ? 'desc' : 'asc'
      });
    } else {
      // New column, default to ascending
      setSortConfig({ key, direction: 'asc' });
    }
  };



  const getStatusBadge = (status: string) => {
    const statusClasses = {
      'Pending': 'bg-yellow-100 text-yellow-800',
      'In Progress': 'bg-blue-100 text-blue-800',
      'Completed': 'bg-green-100 text-green-800',
      'On Hold': 'bg-red-100 text-red-800',
    };
    
    return (
      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClasses[status as keyof typeof statusClasses]}`}>
        {status}
      </span>
    );
  };

  const SortIcon = ({ column }: { column: keyof Inquiry }) => {
    if (!sortConfig || sortConfig.key !== column) {
      return <FiChevronDown className="ml-1 h-4 w-4 text-gray-400" />;
    }
    return sortConfig.direction === 'asc' ? (
      <FiChevronUp className="ml-1 h-4 w-4 text-gray-700" />
    ) : (
      <FiChevronDown className="ml-1 h-4 w-4 text-gray-700" />
    );
  };

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200 dark:divide-slate-700">
        <thead className="bg-gray-50 dark:bg-slate-800">
          <tr>
            {['client', 'year', 'caseNo', 'reference', 'subject', 'status', 'dateRequested', 'deliveryRequired', 'actions'].map((key) => (
              <th
                key={key}
                scope="col"
                className={`px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider ${
                  key !== 'actions' ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-slate-700' : ''
                }`}
                onClick={() => key !== 'actions' && requestSort(key as keyof Inquiry)}
              >
                <div className="flex items-center">
                  {key === 'caseNo' ? 'Case No.' : 
                   key === 'dateRequested' ? 'Date Requested' :
                   key === 'deliveryRequired' ? 'Delivery Required' :
                   key === 'actions' ? 'Actions' :
                   key.charAt(0).toUpperCase() + key.slice(1)}
                  {key !== 'actions' && (
                    <SortIcon column={key as keyof Inquiry} />
                  )}
                </div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-slate-700">
          {isLoading ? (
            <tr>
              <td colSpan={9} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                <div className="flex justify-center py-8">
                  <Loading fullScreen={false} text="Loading inquiries..." size="md" />
                </div>
              </td>
            </tr>
          ) : currentInquiries.length > 0 ? (
            currentInquiries.map((inquiry) => (
              <tr 
                key={inquiry.id} 
                className="group hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors relative cursor-pointer"
                onClick={(e) => {
                  // Don't navigate if clicking on action buttons or dropdown
                  if (!(e.target as HTMLElement).closest('.action-button')) {
                    window.location.href = `/inquiries/${inquiry.id}`;
                  }
                }}
                onKeyDown={(e) => {
                  // Add keyboard navigation support (Enter/Space to click)
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    window.location.href = `/inquiries/${inquiry.id}`;
                  }
                }}
                role="button"
                tabIndex={0}
                aria-label={`View details for ${inquiry.client}'s inquiry`}
              >
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white cursor-pointer">
                  <div className="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                    {inquiry.client}
                    <FiExternalLink className="ml-1 h-3 w-3 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </div>
                </td>

                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300 group-hover:bg-gray-50 dark:group-hover:bg-slate-700">
                  {inquiry.year}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300 group-hover:bg-gray-50 dark:group-hover:bg-slate-700">
                  {inquiry.caseNo}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300 group-hover:bg-gray-50 dark:group-hover:bg-slate-700">
                  {inquiry.reference}
                </td>
                <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300 max-w-xs truncate group-hover:bg-gray-50 dark:group-hover:bg-slate-700">
                  {inquiry.subject}
                </td>
                <td className="px-6 py-4 whitespace-nowrap group-hover:bg-gray-50 dark:group-hover:bg-slate-700">
                  {getStatusBadge(inquiry.status)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300 group-hover:bg-gray-50 dark:group-hover:bg-slate-700">
                  {new Date(inquiry.dateRequested).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                  })}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300 group-hover:bg-gray-50 dark:group-hover:bg-slate-700">
                  {new Date(inquiry.deliveryRequired).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button 
                    className="action-button text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                    onClick={(e) => {
                      e.stopPropagation();
                      window.location.href = `/inquiries/${inquiry.id}`;
                    }}
                  >
                    View
                  </button>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan={9} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                No inquiries found
              </td>
            </tr>
          )}
        </tbody>
      </table>
      
      {/* Pagination */}
      <div className="bg-white dark:bg-slate-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-slate-700 sm:px-6">
        <div className="flex-1 flex justify-between sm:hidden">
          <button
            onClick={() => currentPage > 1 && paginate(currentPage - 1)}
            disabled={currentPage === 1}
            className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-slate-700 dark:border-slate-600 dark:text-gray-300"
          >
            Previous
          </button>
          <button
            onClick={() => currentPage < totalPages && paginate(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-slate-700 dark:border-slate-600 dark:text-gray-300"
          >
            Next
          </button>
        </div>
        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p className="text-sm text-gray-700 dark:text-gray-300">
              Showing <span className="font-medium">
                {filteredAndSortedInquiries.length === 0 ? 0 : indexOfFirstInquiry + 1}
              </span> to{' '}
              <span className="font-medium">
                {Math.min(indexOfLastInquiry, filteredAndSortedInquiries.length)}
              </span>{' '}
              of <span className="font-medium">{filteredAndSortedInquiries.length}</span> results
              {searchTerm && ` matching "${searchTerm}"`}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <div className="flex items-center text-sm text-gray-700 dark:text-gray-300">
              Rows per page:
              <select
                value={itemsPerPage}
                onChange={handleItemsPerPageChange}
                className="ml-2 block w-16 pl-3 pr-10 py-1 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md dark:bg-slate-700 dark:border-slate-600 dark:text-white"
              >
                <option value={5}>5</option>
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
              </select>
            </div>
            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                onClick={() => currentPage > 1 && paginate(currentPage - 1)}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 dark:bg-slate-700 dark:border-slate-600 dark:text-gray-300 dark:hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span className="sr-only">Previous</span>
                <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </button>
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = currentPage - 2 + i;
                }
                
                return (
                  <button
                    key={pageNum}
                    onClick={() => paginate(pageNum)}
                    className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                      currentPage === pageNum
                        ? 'z-10 bg-blue-50 border-blue-500 text-blue-600 dark:bg-blue-900/30 dark:border-blue-700 dark:text-blue-400'
                        : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50 dark:bg-slate-700 dark:border-slate-600 dark:text-gray-300 dark:hover:bg-slate-600'
                    }`}
                  >
                    {pageNum}
                  </button>
                );
              })}
              <button
                onClick={() => currentPage < totalPages && paginate(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 dark:bg-slate-700 dark:border-slate-600 dark:text-gray-300 dark:hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span className="sr-only">Next</span>
                <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>
  );
};

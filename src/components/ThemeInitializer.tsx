'use client';

import { useEffect } from 'react';

export function ThemeInitializer() {
  useEffect(() => {
    // This runs on the client side after hydration
    const theme = localStorage.getItem('ascension-theme');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    if (theme === 'dark' || (!theme && prefersDark)) {
      document.documentElement.classList.add('dark');
      document.documentElement.style.backgroundColor = '#111827';
    } else {
      document.documentElement.classList.remove('dark');
      document.documentElement.style.backgroundColor = '#ffffff';
    }
  }, []);

  return null;
}

'use client';

import { ReactNode, useState } from 'react';
import AppSidebar, { MobileMenuButton } from './AppSidebar';
import AppHeader from './AppHeader';
import { cn } from '@/lib/utils';

interface MainLayoutProps {
  children: ReactNode;
  className?: string;
}

export function MainLayout({ children, className }: MainLayoutProps) {
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  const toggleMobileSidebar = () => {
    setIsMobileSidebarOpen(!isMobileSidebarOpen);
  };

  const closeMobileSidebar = () => {
    setIsMobileSidebarOpen(false);
  };

  return (
    <div className={cn(
      'flex h-screen bg-gray-50 dark:bg-slate-900',
      className
    )}>
      {/* Mobile sidebar overlay */}
      {isMobileSidebarOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 md:hidden" 
          onClick={closeMobileSidebar}
          aria-hidden="true"
        />
      )}
      
      {/* Sidebar */}
      <AppSidebar 
        isMobileOpen={isMobileSidebarOpen} 
        onMobileClose={closeMobileSidebar} 
      />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <div className="flex items-center h-16 bg-[#2A6E78] shadow-sm">
          <MobileMenuButton onClick={toggleMobileSidebar} />
          <AppHeader />
        </div>
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 dark:bg-slate-900 p-6">
          {children}
        </main>
      </div>
    </div>
  );
}

'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { FiInbox, FiSend, FiStar, FiTrash2, FiFileText, FiSettings } from 'react-icons/fi';

const navigation = [
  { name: 'Inbox', href: '/messages', icon: FiInbox, count: '12' },
  { name: 'Starred', href: '/messages/starred', icon: FiStar },
  { name: 'Sent', href: '/messages/sent', icon: FiSend },
  { name: 'Drafts', href: '/messages/drafts', icon: FiFileText, count: '3' },
  { name: 'Trash', href: '/messages/trash', icon: FiTrash2 },
  { name: 'Settings', href: '/messages/settings', icon: FiSettings },
];

export function MessagesSidebar() {
  const pathname = usePathname();

  return (
    <div className="flex h-full w-64 flex-col border-r border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800">
      <div className="flex h-16 flex-shrink-0 items-center border-b border-gray-200 px-4 dark:border-gray-700">
        <h1 className="text-lg font-semibold text-gray-900 dark:text-white">Messages</h1>
      </div>
      <nav className="flex-1 space-y-1 overflow-y-auto p-4">
        {navigation.map((item) => {
          const isActive = pathname === item.href;
          return (
            <Link
              key={item.name}
              href={item.href}
              className={`group flex items-center rounded-md px-3 py-2 text-sm font-medium ${
                isActive
                  ? 'bg-blue-50 text-blue-700 dark:bg-gray-700 dark:text-white'
                  : 'text-gray-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-700'
              }`}
            >
              <item.icon
                className={`mr-3 h-5 w-5 flex-shrink-0 ${
                  isActive ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500 dark:text-gray-400'
                }`}
                aria-hidden="true"
              />
              <span className="flex-1">{item.name}</span>
              {item.count && (
                <span
                  className={`ml-3 inline-block rounded-full px-2 py-0.5 text-xs font-medium ${
                    isActive
                      ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100'
                      : 'bg-gray-100 text-gray-800 group-hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'
                  }`}
                >
                  {item.count}
                </span>
              )}
            </Link>
          );
        })}
      </nav>
      <div className="border-t border-gray-200 p-4 dark:border-gray-700">
        <div className="flex items-center">
          <div className="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium">
            U
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-gray-700 dark:text-gray-200">User Name</p>
            <p className="text-xs text-gray-500 dark:text-gray-400"><EMAIL></p>
          </div>
        </div>
      </div>
    </div>
  );
}

import { useState } from 'react';
import { cn } from '@/lib/utils';

interface MenuButtonProps {
  label: string;
  onClick?: () => void;
  className?: string;
  children?: React.ReactNode;
}

const MenuButton = ({ label, onClick, className, children }: MenuButtonProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const hasChildren = Boolean(children);

  return (
    <div className="relative">
      <button
        onClick={() => {
          if (hasChildren) {
            setIsOpen(!isOpen);
          } else {
            onClick?.();
          }
        }}
        className={cn(
          'px-4 py-2 text-sm font-medium rounded-md',
          'text-gray-700 hover:bg-gray-100',
          'dark:text-gray-200 dark:hover:bg-gray-700',
          'transition-colors duration-150',
          isOpen && 'bg-gray-100 dark:bg-gray-700',
          className
        )}
      >
        <div className="flex items-center space-x-1">
          <span>{label}</span>
          {hasChildren && (
            <svg
              className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          )}
        </div>
      </button>
      {hasChildren && isOpen && (
        <div className="absolute left-0 mt-1 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-50 border border-gray-200 dark:border-gray-700">
          {children}
        </div>
      )}
    </div>
  );
};

const MenuBar = () => {
  const [zoom, setZoom] = useState(100);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showLogs, setShowLogs] = useState(false);

  const handlePrint = () => {
    window.print();
  };

  const handleZoom = (direction: 'in' | 'out' | 'reset') => {
    if (direction === 'in' && zoom < 150) {
      setZoom(prev => prev + 10);
    } else if (direction === 'out' && zoom > 50) {
      setZoom(prev => prev - 10);
    } else if (direction === 'reset') {
      setZoom(100);
    }
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch(console.error);
      setIsFullscreen(true);
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen().catch(console.error);
        setIsFullscreen(false);
      }
    }
  };

  const handleExportLogs = () => {
    // In a real app, this would export the logs
    const logs = "Event log data would be exported here";
    const blob = new Blob([logs], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `event-logs-${new Date().toISOString().slice(0,10)}.log`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleClearLogs = () => {
    // In a real app, this would clear the logs
    if (confirm('Are you sure you want to clear all logs?')) {
      // Clear logs logic here
      console.log('Logs cleared');
    }
  };

  return (
    <div className="flex items-center space-x-1">
      <MenuButton label="File">
        <button className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700">
          New
        </button>
        <button className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700">
          Open
        </button>
        <button className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700">
          Save
        </button>
      </MenuButton>
      
      <MenuButton 
        label="Print" 
        onClick={handlePrint}
      />
      
      <MenuButton label="View">
        <button 
          onClick={() => handleZoom('in')}
          disabled={zoom >= 150}
          className="w-full text-left px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Zoom In
        </button>
        <button 
          onClick={() => handleZoom('out')}
          disabled={zoom <= 50}
          className="w-full text-left px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Zoom Out
        </button>
        <button 
          onClick={() => handleZoom('reset')}
          disabled={zoom === 100}
          className="w-full text-left px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Reset Zoom ({zoom}%)
        </button>
        <div className="border-t border-gray-200 dark:border-gray-700 my-0.5"></div>
        <button 
          onClick={toggleFullscreen}
          className="w-full text-left px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700"
        >
          {isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}
        </button>
      </MenuButton>

      <MenuButton label="Event Log">
        <button 
          onClick={() => setShowLogs(!showLogs)}
          className="w-full text-left px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700"
        >
          {showLogs ? 'Hide Logs' : 'View Logs'}
        </button>
        <button 
          onClick={handleExportLogs}
          className="w-full text-left px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700"
        >
          Export Logs
        </button>
        <div className="border-t border-gray-200 dark:border-gray-700 my-0.5"></div>
        <button 
          onClick={handleClearLogs}
          className="w-full text-left px-3 py-1.5 text-sm text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-gray-700"
        >
          Clear Logs
        </button>
      </MenuButton>
      
      <MenuButton label="Tools">
        <button 
          onClick={() => console.log('Settings clicked')}
          className="w-full text-left px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700"
        >
          Settings
        </button>
        <button 
          onClick={() => console.log('Extensions clicked')}
          className="w-full text-left px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700"
        >
          Extensions
        </button>
        <div className="border-t border-gray-200 dark:border-gray-700 my-0.5"></div>
        <button 
          onClick={() => alert('Press Ctrl+/ to view keyboard shortcuts')}
          className="w-full text-left px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700"
        >
          Keyboard Shortcuts
        </button>
      </MenuButton>

      <MenuButton label="Help">
        <button 
          onClick={() => window.open('https://docs.yourapp.com', '_blank')}
          className="w-full text-left px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700"
        >
          Documentation
        </button>
        <button 
          onClick={() => window.open('https://tutorials.yourapp.com', '_blank')}
          className="w-full text-left px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700"
        >
          Tutorials
        </button>
        <div className="border-t border-gray-200 dark:border-gray-700 my-0.5"></div>
        <button 
          onClick={() => alert('App Name v1.0.0\n© 2025 Your Company')}
          className="w-full text-left px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700"
        >
          About
        </button>
      </MenuButton>
    </div>
  );
};

export default MenuBar;

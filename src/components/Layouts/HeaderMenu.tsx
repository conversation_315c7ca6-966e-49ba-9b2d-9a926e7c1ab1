'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarSeparator,
  MenubarShortcut,
  MenubarTrigger,
} from '@/components/ui/menubar';
import { FiChevronDown, FiFile, FiTool, FiPrinter, FiList, FiEye, FiHelpCircle, FiBriefcase } from 'react-icons/fi';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface HeaderMenuProps {
  isMobile?: boolean;
}

export function HeaderMenu({ isMobile = false }: HeaderMenuProps) {
  const [isOpen, setIsOpen] = useState(false);
  
  // Mobile dropdown menu
  if (isMobile) {
    return (
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger className="flex items-center px-3 py-2 text-white rounded-md hover:bg-black/10 focus:outline-none">
          <span className="mr-1">Menu</span>
          <FiChevronDown />
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-56">
          <DropdownMenuLabel>Sidebar Toggle</DropdownMenuLabel>
          <DropdownMenuItem asChild>
            <Link href="/dashboard" className="w-full">
              <FiList className="mr-2 h-4 w-4" />
              Dashboard
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link href="/projects" className="w-full">
              <FiBriefcase className="mr-2 h-4 w-4" />
              Projects
            </Link>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuLabel>File</DropdownMenuLabel>
          <DropdownMenuItem>
            <FiFile className="mr-2 h-4 w-4" />
            New Tab
          </DropdownMenuItem>
          <DropdownMenuItem>
            <FiFile className="mr-2 h-4 w-4" />
            New Window
          </DropdownMenuItem>
          <DropdownMenuItem disabled>
            <FiFile className="mr-2 h-4 w-4" />
            New Incognito Window
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuLabel>Tools</DropdownMenuLabel>
          <DropdownMenuItem>
            <FiTool className="mr-2 h-4 w-4" />
            Save Page As...
          </DropdownMenuItem>
          <DropdownMenuItem>
            <FiTool className="mr-2 h-4 w-4" />
            Developer Tools
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuLabel>Print</DropdownMenuLabel>
          <DropdownMenuItem>
            <FiPrinter className="mr-2 h-4 w-4" />
            Print Page
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuLabel>View</DropdownMenuLabel>
          <DropdownMenuItem>
            <FiEye className="mr-2 h-4 w-4" />
            Reload
          </DropdownMenuItem>
          <DropdownMenuItem>
            <FiEye className="mr-2 h-4 w-4" />
            Toggle Fullscreen
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuLabel>Help</DropdownMenuLabel>
          <DropdownMenuItem>
            <FiHelpCircle className="mr-2 h-4 w-4" />
            Documentation
          </DropdownMenuItem>
          <DropdownMenuItem>
            <FiHelpCircle className="mr-2 h-4 w-4" />
            Support
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }
  
  // Desktop menubar
  return (
    <Menubar className="border-none bg-transparent rounded-none">
      <MenubarMenu>
        <MenubarTrigger className="text-white focus:bg-black/10 data-[state=open]:bg-black/10">Navigation</MenubarTrigger>
        <MenubarContent>
          <MenubarItem>
            <Link href="/dashboard">Dashboard</Link>
          </MenubarItem>
          <MenubarItem>
            <Link href="/projects">Projects</Link>
          </MenubarItem>
        </MenubarContent>
      </MenubarMenu>
      <MenubarMenu>
        <MenubarTrigger className="text-white focus:bg-black/10 data-[state=open]:bg-black/10">File</MenubarTrigger>
        <MenubarContent>
          <MenubarItem>New Tab <MenubarShortcut>⌘T</MenubarShortcut></MenubarItem>
          <MenubarItem>New Window <MenubarShortcut>⌘N</MenubarShortcut></MenubarItem>
          <MenubarItem disabled>New Incognito Window</MenubarItem>
          <MenubarSeparator />
          <MenubarItem>Share</MenubarItem>
          <MenubarSeparator />
          <MenubarItem>Print <MenubarShortcut>⌘P</MenubarShortcut></MenubarItem>
        </MenubarContent>
      </MenubarMenu>
      <MenubarMenu>
        <MenubarTrigger className="text-white focus:bg-black/10 data-[state=open]:bg-black/10">Tools</MenubarTrigger>
        <MenubarContent>
          <MenubarItem>Save Page As... <MenubarShortcut>⇧⌘S</MenubarShortcut></MenubarItem>
          <MenubarItem>Create Shortcut...</MenubarItem>
          <MenubarItem>Name Window...</MenubarItem>
          <MenubarSeparator />
          <MenubarItem>Developer Tools</MenubarItem>
        </MenubarContent>
      </MenubarMenu>
      <MenubarMenu>
        <MenubarTrigger className="text-white focus:bg-black/10 data-[state=open]:bg-black/10">Print</MenubarTrigger>
        <MenubarContent>
          <MenubarItem>Print Page <MenubarShortcut>⌘P</MenubarShortcut></MenubarItem>
        </MenubarContent>
      </MenubarMenu>
      <MenubarMenu>
        <MenubarTrigger className="text-white focus:bg-black/10 data-[state=open]:bg-black/10">Event log</MenubarTrigger>
        <MenubarContent>
          <MenubarItem>View Logs</MenubarItem>
          <MenubarItem>Clear Logs</MenubarItem>
        </MenubarContent>
      </MenubarMenu>
      <MenubarMenu>
        <MenubarTrigger className="text-white focus:bg-black/10 data-[state=open]:bg-black/10">View</MenubarTrigger>
        <MenubarContent>
          <MenubarItem>Reload <MenubarShortcut>⌘R</MenubarShortcut></MenubarItem>
          <MenubarItem>Force Reload <MenubarShortcut>⇧⌘R</MenubarShortcut></MenubarItem>
          <MenubarSeparator />
          <MenubarItem>Toggle Fullscreen</MenubarItem>
        </MenubarContent>
      </MenubarMenu>
      <MenubarMenu>
        <MenubarTrigger className="text-white focus:bg-black/10 data-[state=open]:bg-black/10">Help</MenubarTrigger>
        <MenubarContent>
          <MenubarItem>Documentation</MenubarItem>
          <MenubarItem>Support</MenubarItem>
        </MenubarContent>
      </MenubarMenu>
    </Menubar>
  );
}

'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { FiFilter, FiX, FiChevronDown, FiChevronUp } from 'react-icons/fi';
import { Logo } from '@/components/logo';
import { useSupplierDatabase } from '@/context/SupplierDatabaseContext';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';

interface SupplierDatabaseSidebarProps {
  onClose?: () => void;
}

const SupplierDatabaseSidebar = ({ onClose }: SupplierDatabaseSidebarProps) => {
  const pathname = usePathname();
  const [isPrimaryFiltersOpen, setPrimaryFiltersOpen] = useState(true);
  const [isCategoryFiltersOpen, setCategoryFiltersOpen] = useState(true);
  
  const { 
    checkedPrimary, 
    handlePrimaryChange, 
    checkedCategories, 
    handleCategoryChange,
    clearFilters
  } = useSupplierDatabase();

  const primaryFilters = [
    'Prequalified suppliers',
    'PPDA registered',
    'PAYE registered',
    'VAT registered'
  ];

  const businessCategories = [
    'Consultancy Services',
    'Non-consultancy Services',
    'Manufacturer',
    'General Merchandise',
    'ICT Hardware & Software',
    'Electricals & Electronics',
    'Furniture & Fittings',
    'Hardware & Construction',
    'Leisure & Accommodation',
    'Automotive Works & Spares',
    'Agricultural Tools & Produce',
    'Medical & Healthcare',
    'Mechanical Tools & Equipment',
    'Oil & Gas Products',
    'Marine Supplies'
  ];

  return (
    <div className="max-h-screen h-screen flex flex-col bg-white dark:bg-slate-800 border-r border-gray-200 dark:border-slate-700 overflow-hidden">
      {/* Sidebar Header with Logo - matching main header style */}
      <div className="flex items-center justify-between p-2 bg-[#18546c] text-white border-b border-white/10">
        <Link href="/supplier-database" className="flex items-center justify-center">
          <Logo variant="dark" />
        </Link>
        
        {/* Mobile Close Button */}
        {onClose && (
          <button 
            onClick={onClose}
            className="p-2 rounded-full hover:bg-white/10 focus:outline-none"
            aria-label="Close sidebar"
          >
            <FiX className="h-2 w-2 text-white" />
          </button>
        )}
      </div>
      
      {/* Filters */}
      <div className="flex-1 overflow-y-auto p-4" style={{ maxHeight: 'calc(100vh - 120px)' }}>
        <div className="mb-6 mt-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white flex items-center">
              <FiFilter className="mr-2 h-4 w-4" />
              Primary filters
            </h3>
            <button 
              onClick={() => setPrimaryFiltersOpen(!isPrimaryFiltersOpen)}
              className="p-1 rounded-md hover:bg-gray-100 dark:hover:bg-slate-700"
            >
              {isPrimaryFiltersOpen ? (
                <FiChevronUp className="h-4 w-4 text-gray-500 dark:text-gray-400" />
              ) : (
                <FiChevronDown className="h-4 w-4 text-gray-500 dark:text-gray-400" />
              )}
            </button>
          </div>
          
          {isPrimaryFiltersOpen && (
            <div className="space-y-2">
              {primaryFilters.map((filter) => (
                <div 
                  key={filter} 
                  className={`px-3 py-2.5 rounded-md cursor-pointer transition-colors text-xs ${
                    checkedPrimary[filter] 
                      ? 'bg-[#18546c] text-white font-medium' 
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-700'
                  }`}
                  onClick={() => handlePrimaryChange(filter)}
                >
                  {filter}
                </div>
              ))}
            </div>
          )}
        </div>
        
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white">
              Main business category
            </h3>
            <button 
              onClick={() => setCategoryFiltersOpen(!isCategoryFiltersOpen)}
              className="p-1 rounded-md hover:bg-gray-100 dark:hover:bg-slate-700"
            >
              {isCategoryFiltersOpen ? (
                <FiChevronUp className="h-4 w-4 text-gray-500 dark:text-gray-400" />
              ) : (
                <FiChevronDown className="h-4 w-4 text-gray-500 dark:text-gray-400" />
              )}
            </button>
          </div>
          
          {isCategoryFiltersOpen && (
            <div className="max-h-[350px] overflow-y-auto pr-1" style={{ scrollbarWidth: 'thin' }}>
              <div className="space-y-2">
                {businessCategories.map((category) => (
                  <div 
                    key={category} 
                    className={`px-3 py-2.5 rounded-md cursor-pointer transition-colors text-xs ${
                      checkedCategories[category] 
                        ? 'bg-[#18546c] text-white font-medium' 
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-700'
                    }`}
                    onClick={() => handleCategoryChange(category)}
                  >
                    {category}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Clear Filters Button */}
      <div className="p-4 border-t border-gray-200 dark:border-slate-700">
        <Button 
          variant="outline" 
          size="sm" 
          className="w-full"
          onClick={clearFilters}
        >
          Clear all filters
        </Button>
      </div>
    </div>
  );
};

export default SupplierDatabaseSidebar;

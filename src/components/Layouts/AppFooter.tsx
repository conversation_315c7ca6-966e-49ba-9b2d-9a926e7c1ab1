import { cn } from '@/lib/utils';

export function AppFooter({ className }: { className?: string }) {
  return (
    <footer className={cn('border-t border-gray-200 bg-white px-4 py-4 dark:border-gray-700 dark:bg-gray-800', className)}>
      <div className="container mx-auto flex items-center justify-between">
        <p className="text-sm text-gray-500 dark:text-gray-400">
          © {new Date().getFullYear()} Ascension. All rights reserved.
        </p>
        <div className="flex space-x-4">
          <a href="#" className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
            Terms
          </a>
          <a href="#" className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
            Privacy
          </a>
          <a href="#" className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
            Contact
          </a>
        </div>
      </div>
    </footer>
  );
}

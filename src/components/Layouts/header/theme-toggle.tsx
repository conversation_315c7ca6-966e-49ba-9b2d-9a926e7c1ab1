'use client';

import { MoonIcon, SunIcon } from './icons';
import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';

export function ThemeToggleSwitch() {
  const [mounted, setMounted] = useState(false);
  const { theme, setTheme } = useTheme();

  // Ensure we're on the client before rendering
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="h-10 w-10 rounded-full border border-stroke dark:border-stroke-dark flex items-center justify-center">
        <div className="h-5 w-5" />
      </div>
    );
  }

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  return (
    <button
      onClick={toggleTheme}
      className="flex h-10 w-10 items-center justify-center rounded-full border border-stroke bg-white dark:border-stroke-dark dark:bg-dark-2"
      aria-label={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
    >
      {theme === 'dark' ? (
        <SunIcon className="h-5 w-5 text-white" />
      ) : (
        <MoonIcon className="h-5 w-5 text-dark" />
      )}
    </button>
  );
}

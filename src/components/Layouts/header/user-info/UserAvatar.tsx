"use client";

import React from 'react';

interface UserAvatarProps {
  initials: string;
  size?: 'sm' | 'md' | 'lg';
}

export const UserAvatar: React.FC<UserAvatarProps> = ({ 
  initials, 
  size = 'md' 
}) => {
  // Size classes
  const sizeClasses = {
    sm: 'size-8 text-sm',
    md: 'size-12 text-lg',
    lg: 'size-16 text-xl'
  };

  return (
    <div 
      className={`flex items-center justify-center rounded-full font-medium text-white shadow-md ${sizeClasses[size]} bg-gradient-to-br from-blue-600 to-indigo-800 border-2 border-white dark:border-gray-800`}
      style={{ 
        backgroundImage: 'linear-gradient(135deg, #14546c 0%, #123e50 100%)',
      }}
      aria-hidden="true"
    >
      {initials}
    </div>
  );
};

export default UserAvatar;

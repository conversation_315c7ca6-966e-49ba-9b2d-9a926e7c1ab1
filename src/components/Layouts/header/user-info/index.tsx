"use client";

import { ChevronUpIcon } from "@/assets/icons";
import {
  Dropdown,
  DropdownContent,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import { cn } from "@/lib/utils";
import { useAuth } from "@/context/AuthContext";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useState, useEffect } from "react";
import { LogOutIcon, MoonIcon, SettingsIcon, SunIcon, UserIcon } from "./icons";
import { UserAvatar } from "./UserAvatar";
import { useTheme } from "next-themes";

export function UserInfo() {
  const [isOpen, setIsOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const { theme, setTheme } = useTheme();
  const { user, logout } = useAuth();
  const router = useRouter();

  // Avoid hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };
  
  // Get initials for the avatar - using first letter of first and last name
  const getInitials = () => {
    console.log('UserInfo: Current user data:', user);
    
    // Use first letter of first and last name if available
    if (user?.firstName || user?.lastName) {
      const first = user.firstName?.[0]?.toUpperCase() || '';
      const last = user.lastName?.[0]?.toUpperCase() || '';
      const initials = first + last;
      
      if (initials) {
        console.log(`UserInfo: Using name initials: ${initials}`);
        return initials;
      }
    }
    
    // Fallback to first two letters of email if no name available
    if (user?.email) {
      const email = user.email.trim();
      if (email.length > 0) {
        const initials = email.length >= 2 ? email.substring(0, 2).toUpperCase() : email[0].toUpperCase();
        console.log(`UserInfo: Using email initials as fallback: ${initials}`);
        return initials;
      }
    }
    
    console.log('UserInfo: No name or email available, using fallback');
    return 'AU'; // Ascension User fallback
  };
  
  // Handle logout
  const handleLogout = () => {
    setIsOpen(false);
    logout();
    router.push('/login');
  };
  
  // Format display name
  const displayName = user?.firstName && user?.lastName
    ? `${user.firstName} ${user.lastName}`
    : user?.firstName || user?.email || "Guest User";

  return (
    <Dropdown isOpen={isOpen} setIsOpen={setIsOpen}>
      <DropdownTrigger className="rounded align-middle outline-none ring-primary ring-offset-2 focus-visible:ring-1 dark:ring-offset-gray-dark">
        <span className="sr-only">My Account</span>

        <figure className="flex items-center gap-2">
          <UserAvatar initials={getInitials()} size="md" />
          <ChevronUpIcon
            aria-hidden
            className={cn(
              "rotate-180 transition-transform text-dark dark:text-dark-6",
              isOpen && "rotate-0",
            )}
            strokeWidth={1.5}
          />
        </figure>
      </DropdownTrigger>

      <DropdownContent
        className="border border-stroke bg-white shadow-md dark:border-dark-3 dark:bg-gray-dark min-[230px]:min-w-[17.5rem]"
        align="end"
      >
        <h2 className="sr-only">User information</h2>

        <figure className="flex items-center gap-2.5 px-5 py-3.5">
          <UserAvatar initials={getInitials()} size="md" />

          <figcaption className="text-base font-medium">
            <div className="leading-none text-dark dark:text-white">
              {displayName}
            </div>
          </figcaption>
        </figure>

        <hr className="border-[#E8E8E8] dark:border-dark-3" />

        <div className="p-2 text-base text-[#4B5563] dark:text-dark-6 [&>*]:cursor-pointer">
          <Link
            href={"/profile"}
            onClick={() => setIsOpen(false)}
            className="flex w-full items-center gap-2.5 rounded-lg px-2.5 py-[9px] hover:bg-gray-2 hover:text-dark dark:hover:bg-dark-3 dark:hover:text-white"
          >
            <UserIcon />

            <span className="mr-auto text-base font-medium">View profile</span>
          </Link>

          <Link
            href={"/pages/settings"}
            onClick={() => setIsOpen(false)}
            className="flex w-full items-center gap-2.5 rounded-lg px-2.5 py-[9px] hover:bg-gray-2 hover:text-dark dark:hover:bg-dark-3 dark:hover:text-white"
          >
            <SettingsIcon />

            <span className="mr-auto text-base font-medium">
              Account Settings
            </span>
          </Link>
        </div>

        <hr className="border-[#E8E8E8] dark:border-dark-3" />

        <div className="p-2 text-base text-[#4B5563] dark:text-dark-6">
          <button
            onClick={toggleTheme}
            className="flex w-full items-center gap-2.5 rounded-lg px-2.5 py-[9px] hover:bg-gray-2 hover:text-dark dark:hover:bg-dark-3 dark:hover:text-white"
          >
            {mounted && (theme === 'dark' ? (
              <>
                <SunIcon className="h-5 w-5" />
                <span className="text-base font-medium">Light Mode</span>
              </>
            ) : (
              <>
                <MoonIcon className="h-5 w-5" />
                <span className="text-base font-medium">Dark Mode</span>
              </>
            ))}
          </button>
        </div>

        <hr className="border-[#E8E8E8] dark:border-dark-3" />

        <div className="p-2 text-base text-[#4B5563] dark:text-dark-6">
          <button
            className="flex w-full items-center gap-2.5 rounded-lg px-2.5 py-[9px] hover:bg-gray-2 hover:text-dark dark:hover:bg-dark-3 dark:hover:text-white"
            onClick={handleLogout}
          >
            <LogOutIcon />
            <span className="text-base font-medium">Log out</span>
          </button>
        </div>
      </DropdownContent>
    </Dropdown>
  );
}

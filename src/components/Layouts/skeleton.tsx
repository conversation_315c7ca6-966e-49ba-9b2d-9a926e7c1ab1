'use client';

import { Skeleton } from "@/components/ui/skeleton";

// Match the exact structure of your actual dashboard content
export function DashboardSkeleton() {
  return (
    <main className="mx-auto w-full max-w-screen-2xl p-4 md:p-6 2xl:p-10">
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="space-y-4 rounded-lg border p-6">
            <div className="flex items-center space-x-4">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-3 w-16" />
              </div>
            </div>
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>
        ))}
      </div>
    </main>
  );
}

// Match the exact structure of your actual sidebar
export function SidebarSkeleton() {
  return (
    <aside className="flex h-screen w-64 flex-col overflow-y-hidden border-r bg-white dark:bg-gray-800">
      <div className="flex h-full flex-col space-y-4 p-4">
        <div className="mb-8 px-4">
          <Skeleton className="h-8 w-32" />
        </div>
        <nav className="flex-1 space-y-2">
          {[...Array(5)].map((_, i) => (
            <Skeleton key={i} className="h-10 w-full" />
          ))}
        </nav>
        <div className="mt-auto pt-4">
          <Skeleton className="h-10 w-full" />
        </div>
      </div>
    </aside>
  );
}

// Match the exact structure of your actual header
export function HeaderSkeleton() {
  return (
    <header className="sticky top-0 z-30 border-b bg-white dark:bg-gray-900">
      <div className="flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
        <div className="flex items-center space-x-4">
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-6 w-24" />
        </div>
        <div className="flex items-center space-x-4">
          <Skeleton className="h-10 w-10 rounded-full" />
        </div>
      </div>
    </header>
  );
}

'use client';

import React, { useState, useEffect, memo } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Squares2X2Icon,
  BriefcaseIcon,
  UsersIcon,
  EnvelopeIcon,
  ChartBarIcon,
  TrashIcon,
  ArchiveBoxIcon,
  Cog6ToothIcon,
  QuestionMarkCircleIcon,
  ArrowRightOnRectangleIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  Bars3Icon,
  XMarkIcon,
  PlusIcon,
  EyeIcon,
  CircleStackIcon,
  UserPlusIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/20/solid';
import { Logo } from '@/components/logo';
import InviteSuppliersModal from '@/components/Modals/InviteSuppliersModal';

interface AppSidebarProps {
  isMobileOpen?: boolean;
  onMobileClose?: () => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

interface NavSubItem {
  title: string;
  href?: string;
  icon?: React.ComponentType<{ className?: string; size?: number }>;
  className?: string;
  size?: number;
  onClick?: () => void;
  external?: boolean;
}

interface NavItem {
  title: string;
  href?: string;
  icon: React.ComponentType<{ className?: string; size?: number }>;
  items?: NavSubItem[];
  badge?: string;
  divider?: boolean;
}

interface NavSection {
  title?: string;
  items: NavItem[];
}

const AppSidebar = ({ isMobileOpen = false, onMobileClose, isCollapsed = false, onToggleCollapse }: AppSidebarProps) => {
  const pathname = usePathname();
  const [expandedItem, setExpandedItem] = useState<string>('Supplier Database');
  const [isInviteModalOpen, setInviteModalOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Check if we're on mobile on initial render and when window resizes
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Initial check
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  // Auto-expand parent menu if child is active
  useEffect(() => {
    if (pathname.startsWith('/supplier-database') && expandedItem !== 'Supplier Database') {
      setExpandedItem('Supplier Database');
    }
  }, [pathname, expandedItem]);

  const openInviteModal = () => setInviteModalOpen(true);
  const closeInviteModal = () => setInviteModalOpen(false);

  const isActive = (path: string) => pathname === path;
  const isParentActive = (items?: NavSubItem[]) => {
    if (!items) return false;
    return items.some(item => item.href && pathname.startsWith(item.href));
  };

  const toggleExpand = (title: string) => {
    setExpandedItem(prev => prev === title ? '' : title);
  };
  
  // Function to handle navigation item click - alias for backward compatibility
  const toggleExpanded = toggleExpand;

  // Navigation data structure
  const navigationSections: NavSection[] = [
    {
      items: [
        {
          title: 'Dashboard',
          href: '/dashboard',
          icon: Squares2X2Icon,
        },
        {
          title: 'Projects',
          icon: BriefcaseIcon,
          items: [
            {
              title: '0001 Administration',
              href: '/projects/0001',
              icon: BriefcaseIcon,
            },
            {
              title: '0002 Mechanical',
              href: '/projects/0002',
              icon: BriefcaseIcon,
            },
            {
              title: '0003 Civil Works',
              href: '/projects/0003',
              icon: BriefcaseIcon,
            },
            {
              title: '0004 Legal & Audit',
              href: '/projects/0004',
              icon: BriefcaseIcon,
            },
            {
              title: '0005 Design',
              href: '/projects/0005',
              icon: BriefcaseIcon,
            },
          ],
        },
        {
          title: 'Supplier Database',
          icon: UsersIcon,
          items: [
            {
              title: 'View all suppliers',
              href: '/supplier-database',
              icon: CircleStackIcon,
              external: true,
            },
            {
              title: 'Pre-qualified suppliers',
              href: '/supplier-database/pre-qualified',
              icon: EyeIcon,
            },
            {
              title: 'Invitations',
              href: '/supplier-database/invitations',
              icon: EnvelopeIcon,
            },
            {
              title: 'Invite new suppliers',
              icon: UserPlusIcon,
              onClick: openInviteModal,
            },      
            
          ],
        },
        {
          title: 'Messages',
          href: '/messages',
          icon: EnvelopeIcon,
          badge: '3',
        },
        {
          title: 'Reports',
          href: '/reports',
          icon: ChartBarIcon,
        },
        {
          title: 'Bin',
          href: '/bin',
          icon: TrashIcon,
        },
        {
          title: 'Assets & Inventory',
          href: '/inventory',
          icon: ArchiveBoxIcon,
        },
        {
          title: 'Settings',
          href: '/settings',
          icon: Cog6ToothIcon,
          divider: true,
        },
        {
          title: 'Help Centre',
          href: '/help-centre',
          icon: QuestionMarkCircleIcon,
        },
      ],
    },
  ];

  // Sub-item renderer function
  const renderSubItem = (subItem: NavSubItem, isLast: boolean) => {
    const subItemIsActive = subItem.href ? isActive(subItem.href) : false;
    
    return (
      <>
        {/* Horizontal connector line */}
        <div className="absolute left-0 top-1/2 w-3 h-px bg-gray-300 dark:bg-gray-600" />
        
        <Link
          href={subItem.href || '#'}
          className={`flex items-center py-2 pl-4 pr-3 text-sm font-medium rounded-lg transition-colors duration-300 ease-out group ${subItemIsActive ? 'text-[#2a6e78] bg-[#f0f4f8]' : 'text-gray-700 hover:bg-[#f0f4f8] hover:text-[#2a6e78]'} dark:text-gray-300 dark:hover:bg-slate-700/50 dark:hover:text-[#2a6e78]`}
          target={subItem.external ? '_blank' : undefined}
          rel={subItem.external ? 'noopener noreferrer' : undefined}
          onClick={subItem.onClick}
        >
          <span className="truncate">{subItem.title}</span>
        </Link>
      </>
    );
  };

  // Navigation item renderer function
  const renderNavItem = (item: NavItem) => {
    const hasSubItems = item.items && item.items.length > 0;
    const isExpanded = expandedItem === item.title;
    const itemIsActive = item.href ? isActive(item.href) : isParentActive(item.items);

    if (hasSubItems) {
      return (
        <div key={item.title} className="space-y-1">
          <button
            onClick={() => toggleExpand(item.title)}
            className={`flex items-center justify-between w-full py-2.5 text-sm font-medium rounded-lg transition-all duration-300 ease-out group ${itemIsActive ? 'text-[#2a6e78] bg-[#f0f4f8]' : 'text-gray-700 hover:bg-[#f0f4f8] hover:text-[#2a6e78]'} dark:text-gray-300 dark:hover:bg-slate-700/50 dark:hover:text-[#2a6e78] mx-2 px-3 will-change-transform`}
          >
            <div className="flex items-center">
              <item.icon
                className={`mr-3 h-5 w-5 flex-shrink-0 transition-colors duration-300 ease-out ${itemIsActive ? 'text-[#2a6e78]' : 'text-gray-500 group-hover:text-[#2a6e78]'}`}
              />
              <div className="relative">
                <span className="truncate font-medium">{item.title}</span>
                {item.badge && (
                  <span className="absolute -top-2 -right-6 text-[10px] font-medium text-white bg-red-600 px-1 rounded-full inline-flex items-center justify-center min-w-[16px] h-[16px] transform transition-transform duration-300 ease-out hover:scale-110">
                    {item.badge}
                  </span>
                )}
              </div>
            </div>
            <ChevronDownIcon
              className={`h-4 w-4 transition-transform duration-300 ease-out ${
                isExpanded ? 'transform rotate-180' : ''
              } ${itemIsActive ? 'text-[#2a6e78]' : 'text-gray-400'} will-change-transform`}
            />
          </button>

          <div
            className={`mt-1 space-y-1 overflow-hidden transition-all duration-300 ease-out`}
            style={{
              maxHeight: isExpanded ? '500px' : '0px',
              opacity: isExpanded ? 1 : 0,
              willChange: 'max-height, opacity'
            }}
          >
            <div className="relative ml-8 space-y-0.5 py-1">
              {/* Continuous vertical line */}
              <div className="absolute left-0 top-0 bottom-0 w-px bg-gray-300 dark:bg-gray-600 group-hover:bg-[#2a6e78]" />
              {item.items?.map((subItem, index, array) => (
                <div key={subItem.title} className="relative">
                  {renderSubItem(subItem, index === array.length - 1)}
                </div>
              ))}
            </div>
          </div>
        </div>
      );
    }

    // Regular navigation item
    if (item.href) {
      return (
        <Link
          key={item.title}
          href={item.href || '#'}
          onClick={() => isMobile && onMobileClose?.()}
          className={`flex items-center py-2.5 text-sm font-medium rounded-lg transition-all duration-300 ease-out group ${itemIsActive ? 'text-[#2a6e78] bg-[#f0f4f8]' : 'text-gray-700 hover:bg-[#f0f4f8] hover:text-[#2a6e78]'} dark:text-gray-300 dark:hover:bg-slate-700/50 dark:hover:text-[#2a6e78] mx-2 px-3`}
        >
          <item.icon
            className={`mr-3 h-5 w-5 flex-shrink-0 transition-colors duration-300 ease-out ${itemIsActive ? 'text-[#2a6e78]' : 'text-gray-500 group-hover:text-[#2a6e78]'}`}
          />
          <div className="relative">
            <span className="truncate font-medium">{item.title}</span>
            {item.badge && (
              <span className="absolute -top-2 -right-6 text-[10px] font-medium text-white bg-red-600 px-1 rounded-full inline-flex items-center justify-center min-w-[16px] h-[16px] transform transition-transform duration-300 ease-out hover:scale-110">
                {item.badge}
              </span>
            )}
          </div>
        </Link>
      );
    }

    return (
      <Link
        key={item.title}
        href={item.href || '#'}
        prefetch={false} // Only prefetch when needed
        className={`flex items-center py-2.5 text-sm font-medium rounded-lg transition-all duration-300 ease-out group ${itemIsActive ? 'text-[#2a6e78] bg-[#f0f4f8]' : 'text-gray-700 hover:bg-[#f0f4f8] hover:text-[#2a6e78]'} dark:text-gray-300 dark:hover:bg-slate-700/50 dark:hover:text-[#2a6e78] mx-2 px-3 will-change-transform`}
      >
        <item.icon
          className={`mr-3 h-5 w-5 flex-shrink-0 transition-transform duration-300 ease-out ${itemIsActive ? 'text-[#2a6e78]' : 'text-gray-500 group-hover:text-[#2a6e78]'} group-hover:scale-110 will-change-transform`}
        />
        <div className="relative">
          <span className="truncate font-medium">{item.title}</span>
          {item.badge && (
            <span className="absolute -top-2 -right-6 text-[10px] font-medium text-white bg-red-600 px-1 rounded-full inline-flex items-center justify-center min-w-[16px] h-[16px] transform transition-transform duration-300 ease-out hover:scale-110">
              {item.badge}
            </span>
          )}
        </div>
      </Link>
    );
  };

  return (
    <div className="relative h-full overflow-visible">
      <aside className={`${isMobile ? 'fixed inset-y-0 left-0 z-50 transform transition-transform duration-300 ease-in-out' : 'relative h-full'} ${isMobile && !isMobileOpen ? '-translate-x-full' : 'translate-x-0'} w-64 min-w-64 max-w-64 flex-shrink-0 bg-white dark:bg-slate-800 shadow-xl ${onToggleCollapse && !isMobile ? '' : 'border-r border-gray-200 dark:border-slate-700'} flex flex-col overflow-visible`}>
      {/* Header */}
      <div className="h-16 flex items-center justify-between bg-[#18546c] px-6 shadow-sm">
        {/* Toggle button for desktop */}
        {!isMobile && onToggleCollapse && (
          <button
            onClick={onToggleCollapse}
            className="text-white p-2 rounded-full hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-white/20 transition-colors"
            aria-label="Toggle sidebar"
          >
            {isCollapsed ? (
              <ChevronRightIcon className="h-5 w-5" />
            ) : (
              <ChevronLeftIcon className="h-5 w-5" />
            )}
          </button>
        )}
        <div className={`flex-1 flex ${!isMobile && onToggleCollapse ? 'justify-center' : 'justify-center'}`}>
          <Logo variant="dark" />
        </div>
        {isMobile && (
          <button
            onClick={onMobileClose}
            className="text-white p-2 rounded-full hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-white/20 transition-colors"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        )}
      </div>
      {/* Navigation */}
      <div className="flex-1 flex flex-col px-2 py-4 overflow-y-auto">
        <nav className="flex-1 space-y-1">
          {navigationSections.map((section, sectionIndex) => (
            <div key={sectionIndex}>
              {section.title && (
                <h3 className="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 mt-6 first:mt-0">
                  {section.title}
                </h3>
              )}
              {section.items.map((item, itemIndex) => (
                <React.Fragment key={item.title}>
                  {item.divider && (
                    <div className="my-8 border-t border-gray-200 dark:border-slate-700" />
                  )}
                  <div className={itemIndex > 0 && !item.divider ? "mt-1" : ""}>
                    {renderNavItem(item)}
                  </div>
                </React.Fragment>
              ))}
            </div>
          ))}
        </nav>

        {/* Logout at the bottom */}
        <div className="mt-auto pt-4 border-t border-gray-200 dark:border-slate-700 px-1">
          <Link
            href="/logout"
            onClick={() => isMobile && onMobileClose?.()}
            className="flex items-center py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group text-gray-700 hover:bg-[#f0f4f8] hover:text-red-600 dark:text-gray-300 dark:hover:bg-slate-700/50 dark:hover:text-red-400 mx-2 px-3"
          >
            <ArrowRightOnRectangleIcon className="mr-3 h-4 w-4 flex-shrink-0 transition-colors text-gray-500 group-hover:text-red-600 dark:group-hover:text-red-400" />
            <span className="truncate font-medium">Logout</span>
          </Link>
        </div>
      </div>
      {/* Invite Suppliers Modal */}
      <InviteSuppliersModal isOpen={isInviteModalOpen} onClose={closeInviteModal} />
    </aside>

    {/* Curved border effect with toggle button */}
    {onToggleCollapse && !isMobile && (
      <div className="absolute top-1/2 right-0 transform -translate-y-1/2 z-20" style={{ width: '20px', height: '60px' }}>
        {/* Curved border using border-radius and pseudo-elements */}
        <div
          className="absolute inset-0 border-r-2 border-gray-300 dark:border-slate-600"
          style={{
            borderRadius: '0 30px 30px 0',
            transform: 'translateX(-1px)'
          }}
        ></div>

        {/* Toggle button */}
        <button
          onClick={onToggleCollapse}
          className="absolute top-1/2 right-0 transform -translate-y-1/2 translate-x-1/2 p-1.5 rounded-full bg-white shadow-lg border border-gray-200 hover:bg-gray-50 hover:shadow-xl transition-all duration-200 dark:bg-slate-800 dark:border-slate-600 dark:hover:bg-slate-700"
          aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          {isCollapsed ? 
            <ChevronRightIcon className="h-3.5 w-3.5 text-gray-500 dark:text-gray-400" /> : 
            <ChevronLeftIcon className="h-3.5 w-3.5 text-gray-500 dark:text-gray-400" />
          }
        </button>
      </div>
    )}
  </div>
  );
};

export const MobileMenuButton = ({ onClick }: { onClick: () => void }) => {
  return (
    <div className="relative md:hidden">
      {/* Curved bend around the button */}
      <div className="absolute -right-3 top-1/2 transform -translate-y-1/2 w-6 h-12 bg-white dark:bg-slate-800 rounded-l-full z-0 shadow-md"></div>
      
      {/* Button with higher z-index to appear above the bend */}
      <button
        onClick={onClick}
        className="relative z-10 p-2 rounded-full text-white hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-white/20 transition-colors"
        aria-label="Open mobile menu"
      >
        <Bars3Icon className="h-5 w-5" />
      </button>
    </div>
  );
};

export default AppSidebar;

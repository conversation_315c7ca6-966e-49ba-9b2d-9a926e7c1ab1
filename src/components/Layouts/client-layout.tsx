'use client';

import { SessionProvider } from 'next-auth/react';
import { ThemeProvider } from '@/components/ThemeProvider';
import { ClientRootLayout } from '@/components/RootLayout';

interface ClientLayoutProps {
  children: React.ReactNode;
}

export default function ClientLayout({ children }: ClientLayoutProps) {
  return (
    <SessionProvider>
      <ThemeProvider>
        <ClientRootLayout>
          {children}
        </ClientRootLayout>
      </ThemeProvider>
    </SessionProvider>
  );
}

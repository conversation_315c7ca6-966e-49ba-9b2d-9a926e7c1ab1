"use client";

import React from 'react'; // Added React import
import { cn } from "@/lib/utils";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { NAV_DATA, LOGOUT_ITEM } from "./data";
import { useAuth } from "@/context/AuthContext";
import { ArrowLeftIcon, ChevronUp } from "./icons";
import { MenuItem } from "./menu-item";
import { useSidebarContext } from "./sidebar-context";
import { Logo } from '@/components/logo';

interface NavSubItem {
  title: string;
  url: string;
}

interface NavItem {
  title: string;
  url?: string;
  icon: React.ComponentType<{ className?: string }>;
  items?: NavSubItem[];
  isDividerBefore?: boolean;
}

// User info component
function UserInfo() {
  const { user } = useAuth();
  const { isOpen } = useSidebarContext();

  if (!user) return null;

  // Get display name (first name, email, or fallback to 'User')
  const displayName = user.firstName?.trim() || 
                     user.email?.split('@')[0] || 
                     'User';

  // Get user role with proper display names
  const getRoleDisplay = (roles?: string[]): string => {
    if (!roles || !roles.length) return 'User';
    
    const role = roles[0];
    switch (role) {
      case 's_admin':
        return 'Supplier Admin';
      case 'c_admin':
        return 'Company Admin';
      default:
        return role.replace('_', ' ');
    }
  };
  
  const userRole = getRoleDisplay(user.roles);

  return (
    <div className="px-4 py-2">
      <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
        {displayName}
      </div>
      <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
        {user.email}
      </div>
      <div className="text-xs text-gray-400 dark:text-gray-500 truncate capitalize">
        {userRole}
      </div>
    </div>
  );
}

export function Sidebar() {
  const pathname = usePathname();
  const { setIsOpen, isOpen, isMobile, toggleSidebar } = useSidebarContext();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const toggleExpanded = useCallback((title: string) => {
    setExpandedItems((prev) => 
      prev.includes(title) 
        ? prev.filter(item => item !== title)
        : [...prev, title]
    );
  }, []);

  useEffect(() => {
    // Keep collapsible open when its subpage is active
    NAV_DATA.forEach((item: NavItem) => {
      if (!item.items) return;
      
      const shouldExpand = item.items.some(
        (subItem) => subItem.url === pathname || (subItem.url !== '/' && pathname.startsWith(`${subItem.url}/`))
      );

      if (shouldExpand && !expandedItems.includes(item.title)) {
        setExpandedItems((prev) => [...prev, item.title]);
      } else if (!shouldExpand && expandedItems.includes(item.title) && !item.items.some(subItem => pathname.startsWith(`${subItem.url}/`))) {
        // Optional: Collapse if no child is active and not explicitly kept open by user click
        // For now, we only expand automatically, user collapses manually or by navigating away from all children
      }
    });
  }, [pathname, expandedItems]); // Added expandedItems to the dependency array to fix the warning

  const renderItemContent = (item: NavItem, isActive: boolean) => {
    const isExpanded = expandedItems.includes(item.title);
    
    return (
      <div className="flex items-center w-full">
        <item.icon
          className={cn(
            "h-5 w-5 flex-shrink-0",
            isActive ? "text-blue-600 dark:text-blue-400" : "text-gray-500 dark:text-gray-400"
          )}
          aria-hidden="true"
        />
        <span className="ml-3 text-sm font-medium">
          {item.title}
        </span>
        {item.items && (
          <ChevronUp
            className={cn(
              "ml-auto h-4 w-4 transition-transform duration-200",
              isActive ? "text-blue-600 dark:text-blue-400" : "text-gray-500 dark:text-gray-400",
              isExpanded ? "rotate-0" : "rotate-90"
            )}
          />
        )}
      </div>
    );
  };

  const isItemActive = (item: NavItem): boolean => {
    // Check if the item's URL matches the current path
    if (item.url && pathname === item.url) return true;
    
    // Check if any child item's URL matches the current path
    if (item.items) {
      return item.items.some(subItem => {
        // Check for exact match or if the current path starts with the item's URL (for nested routes)
        return pathname === subItem.url || 
               (subItem.url !== '/' && pathname.startsWith(`${subItem.url}/`));
      });
    }
    
    return false;
  };

  const renderNavItem = (item: NavItem) => {
    const isActive = isItemActive(item);
    const isExpanded = expandedItems.includes(item.title);
    const hasActiveChild = item.items ? isActive : false;

    return (
      <div key={`${item.title}-container`} className="mb-1">
        {item.items ? (
          <MenuItem
            isActive={isActive || hasActiveChild}
            onClick={() => toggleExpanded(item.title)}
            as="button"
          >
            {renderItemContent(item, isActive || hasActiveChild)}
          </MenuItem>
        ) : (
          <MenuItem
            isActive={isActive}
            as="link"
            href={item.url || '#'}
          >
            {renderItemContent(item, isActive || hasActiveChild)}
          </MenuItem>
        )}
        
        {item.items && isExpanded && (
          <ul className="mt-1 space-y-1 border-l-2 border-gray-100 dark:border-gray-700 pl-4 py-1 ml-2">
              {item.items.map((subItem: NavSubItem) => {
                const isSubItemActive = pathname === subItem.url || 
                                     (subItem.url !== '/' && pathname.startsWith(`${subItem.url}/`));
                return (
                  <li key={subItem.title}>
                    <MenuItem
                      as="link"
                      href={subItem.url}
                      isActive={isSubItemActive}
                      className="pl-5" // Indentation for sub-item, text color handled by MenuItem
                    >
                      {/* Icon can be added here if sub-items need icons */}
                      <span className="text-sm">
                        {subItem.title}
                      </span>
                    </MenuItem>
                  </li>
                );
              })}
          </ul>
        )}
      </div>
    );
  };

  return (
    <>
      {/* Mobile Overlay */}
      {isMobile && isOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 transition-opacity duration-300"
          onClick={() => setIsOpen(false)}
          aria-hidden="true"
        />
      )}

      <aside
        className={cn(
          "w-64 overflow-hidden border-r border-gray-100 dark:border-gray-700 bg-white dark:bg-gray-800 transition-all duration-200 ease-in-out",
          isMobile ? "fixed bottom-0 top-0 z-50" : "sticky top-0 h-screen",
          isOpen ? "w-64" : "w-0 md:w-20"
        )}
        aria-label="Main navigation"
        aria-hidden={!isOpen}
        // @ts-ignore - inert is a valid HTML attribute but TypeScript types don't include it
        inert={!isOpen || undefined}
      >
        <div className="flex h-full flex-col py-6 px-4">
          <div className="flex-1 flex flex-col">
            <div className="mb-8 px-2">
              <Link
                href="/"
                onClick={() => isMobile && toggleSidebar()}
                className="flex items-center space-x-3 px-2"
              >
                <Logo />
              </Link>

              {isMobile && (
                <button
                  onClick={toggleSidebar}
                  className="absolute left-3/4 right-4.5 top-1/2 -translate-y-1/2 text-right"
                  aria-label="Close menu"
                >
                  <ArrowLeftIcon className="ml-auto size-7" />
                </button>
              )}
            </div>

            {/* Navigation */}
            <nav className="custom-scrollbar mt-2 space-y-1 overflow-y-auto">
              <ul className="space-y-1">
                {NAV_DATA.map((item: NavItem, index: number) => (
                  <React.Fragment key={`${item.title}-${index}`}>
                    <li key={item.title}>
                      {renderNavItem(item)}
                    </li>
                  </React.Fragment>
                ))}
              </ul>
            </nav>
          </div>

          {/* User info and Logout button at the bottom */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-4 mt-auto">
            <UserInfo />
            <div className="mt-4 px-2">
              {renderNavItem(LOGOUT_ITEM)}
            </div>
          </div>
        </div>
      </aside>
    </>
  );
}
import { cn } from "@/lib/utils";
import { cva } from "class-variance-authority";
import Link from "next/link";
import { useSidebarContext } from "./sidebar-context";

const menuItemBaseStyles = cva(
  "group relative flex w-full items-center rounded-md px-3 py-2.5 text-sm transition-all duration-200", // Added relative, changed to rounded-md
  {
    variants: {
      isActive: {
        true: "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-100 font-medium",
        false: "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-800 dark:hover:text-gray-100 font-medium",
      },
    },
    defaultVariants: {
      isActive: false,
    },
  },
);

export function MenuItem(
  props: {
    className?: string;
    children: React.ReactNode;
    isActive: boolean;
  } & ({ as?: "button"; onClick: () => void } | { as: "link"; href: string }),
) {
  const { toggleSidebar, isMobile } = useSidebarContext();

  if (props.as === "link") {
    return (
      <Link
        href={props.href}
        // Close sidebar on clicking link if it's mobile
        onClick={() => isMobile && toggleSidebar()}
        className={cn(
          menuItemBaseStyles({
            isActive: props.isActive,
          }),
          props.className, // Apply additional classes
        )}
      >
        {props.isActive && <div className="absolute left-0 top-1 bottom-1 w-1 bg-blue-600 rounded-r-sm" />}
        {props.children}
      </Link>
    );
  }

  return (
    <button 
      type="button" 
      onClick={props.onClick} 
      aria-expanded={props.isActive} 
      className={cn(
        menuItemBaseStyles({ 
          isActive: props.isActive 
        }), 
        props.className
      )}
    >
      {props.isActive && <div className="absolute left-0 top-1 bottom-1 w-1 bg-blue-600 rounded-r-sm" />}
      {props.children}
    </button>
  );
}

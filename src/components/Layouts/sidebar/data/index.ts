import * as Icons from "../icons";

export interface NavSubItem {
  title: string;
  url: string;
}

export interface NavItem {
  title: string;
  url?: string; // Optional for parent items
  icon: React.ComponentType<Icons.PropsType>;
  items?: NavSubItem[];
  isDividerBefore?: boolean; // For rendering a divider before this item
}

// Main navigation items
export const NAV_DATA: NavItem[] = [
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: Icons.HomeIcon,
  },
  {
    title: "Projects",
    icon: Icons.Table,
    items: [
      {
        title: "All Projects",
        url: "/projects",
      },
      {
        title: "Create New",
        url: "/projects/new",
      },
    ],
  },
  {
    title: "Suppliers",
    icon: Icons.User,
    items: [
      {
        title: "View Suppliers",
        url: "/suppliers",
      },
      {
        title: "Add Supplier",
        url: "/suppliers/add",
      },
    ],
  },
  {
    title: "Messages",
    url: "/messages",
    icon: Icons.MessageIcon,
  },
  {
    title: "Reports",
    url: "/reports",
    icon: Icons.PieChart,
  },
  {
    title: "Bin",
    url: "/bin",
    icon: Icons.Alphabet,
  },
  {
    title: "Inventory",
    url: "/inventory",
    icon: Icons.Table,
  },
  {
    title: "Settings",
    url: "/settings",
    icon: Icons.FourCircle,
    isDividerBefore: true,
  },
  {
    title: "Help Centre",
    url: "/help-centre",
    icon: Icons.BellIcon,
    isDividerBefore: true,
  },
];

// Logout item (rendered separately at the bottom)
export const LOGOUT_ITEM: NavItem = {
  title: "Logout",
  url: "/logout",
  icon: Icons.Authentication,
  isDividerBefore: true,
};

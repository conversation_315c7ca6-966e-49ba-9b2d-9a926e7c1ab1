import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { FiHome, FiDatabase, FiMessageSquare, FiFileText, FiTrash2, FiPackage, FiSettings, FiHelpCircle, FiLogOut } from 'react-icons/fi';

interface NavItemProps {
  href: string;
  icon: React.ReactNode;
  label: string;
  isActive: boolean;
  children?: React.ReactNode;
}

const NavItem: React.FC<NavItemProps> = ({ href, icon, label, isActive, children }) => {
  return (
    <li>
      <Link
        href={href}
        className={`flex items-center px-4 py-3 text-sm font-medium rounded-md ${
          isActive
            ? 'bg-blue-50 text-blue-700 dark:bg-slate-700 dark:text-white'
            : 'text-gray-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-slate-700'
        }`}
      >
        <span className="mr-3 text-lg">{icon}</span>
        <span className="truncate">{label}</span>
        {children}
      </Link>
    </li>
  );
};

interface InquiriesSidebarProps {
  onClose?: () => void;
}

export default function InquiriesSidebar({ onClose }: InquiriesSidebarProps) {
  const pathname = usePathname();

  const handleNavClick = () => {
    if (onClose) {
      onClose();
    }
  };

  return (
    <div className="h-full flex flex-col bg-white dark:bg-slate-800 border-r border-gray-200 dark:border-slate-700">
      {/* Logo */}
      <div className="flex items-center justify-center h-16 px-4 border-b border-gray-200 dark:border-slate-700">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">Inquiries</h2>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
        <ul className="space-y-1">
          <NavItem
            href="/dashboard"
            icon={<FiHome />}
            label="Dashboard"
            isActive={pathname === '/dashboard'}
          />
          <NavItem
            href="/supplier-database"
            icon={<FiDatabase />}
            label="Supplier Database"
            isActive={pathname?.startsWith('/supplier-database')}
          />
          <NavItem
            href="/inquiries"
            icon={<FiMessageSquare />}
            label="Inquiries"
            isActive={pathname?.startsWith('/inquiries')}
          />
          <NavItem
            href="/reports"
            icon={<FiFileText />}
            label="Reports"
            isActive={pathname?.startsWith('/reports')}
          />
          <NavItem
            href="/bin"
            icon={<FiTrash2 />}
            label="Bin"
            isActive={pathname?.startsWith('/bin')}
          />
          <NavItem
            href="/assets"
            icon={<FiPackage />}
            label="Assets & Inventory"
            isActive={pathname?.startsWith('/assets')}
          />
        </ul>

        <div className="pt-4 mt-4 border-t border-gray-200 dark:border-slate-700">
          <ul className="space-y-1">
            <NavItem
              href="/settings"
              icon={<FiSettings />}
              label="Settings"
              isActive={pathname?.startsWith('/settings')}
            />
            <NavItem
              href="/help"
              icon={<FiHelpCircle />}
              label="Help Centre"
              isActive={pathname?.startsWith('/help')}
            />
            <NavItem
              href="/logout"
              icon={<FiLogOut />}
              label="Logout"
              isActive={false}
            />
          </ul>
        </div>
      </nav>
    </div>
  );
}

'use client';

import React, { useState, useEffect } from 'react';
import { useTheme } from 'next-themes';
import { MagnifyingGlassIcon, BellIcon, EnvelopeIcon, PrinterIcon, Cog6ToothIcon, ArrowRightOnRectangleIcon, SunIcon, MoonIcon, XMarkIcon, QuestionMarkCircleIcon, ChevronDownIcon } from '@heroicons/react/24/solid';
import { useAuth } from '@/context/AuthContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuGroup, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { HeaderMenu } from './HeaderMenu';
import { Input } from '@/components/ui/input';

interface AppHeaderProps {
  className?: string;
}

const AppHeader = ({ className = '' }: AppHeaderProps) => {
  const [isMobile, setIsMobile] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  
  // Check if we're on mobile on initial render and when window resizes
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    // Initial check
    checkIfMobile();
    
    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Searching for:', searchTerm);
    // Implement your search functionality here
  };
  const { user, logout } = useAuth();
  const { theme, setTheme } = useTheme();

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  const roleMap: { [key: string]: string } = {
    s_admin: 'Supplier Admin',
    c_admin: 'Company Admin',
  };

  const fullName = user?.firstName && user?.lastName ? `${user.firstName} ${user.lastName}` : 'User Name';
  const userRole = user?.roles && user.roles.length > 0 ? user.roles[0] : '';
  const displayRole = roleMap[userRole] || userRole || 'No role assigned';
  const avatarFallback = user?.firstName && user?.lastName ? `${user.firstName.charAt(0)}${user.lastName.charAt(0)}` : 'U';

  return (
    <header className={`h-16 bg-[#18546c] flex-1 flex items-center justify-between px-2 md:px-6 ${className}`}>
      {/* Left side - Main menu */}
      <div className="flex items-center space-x-6">
        {/* Main menu items - hidden on mobile */}
        <div className="hidden md:flex items-center space-x-4 text-white text-sm">
          <button className="hover:bg-black/10 px-2 py-1 rounded">File</button>
          <button className="hover:bg-black/10 px-2 py-1 rounded">Tools</button>
          <button className="hover:bg-black/10 px-2 py-1 rounded">Print</button>
          <button className="hover:bg-black/10 px-2 py-1 rounded flex items-center">
            Event log
            <ChevronDownIcon className="h-4 w-4 ml-1" />
          </button>
          <button className="hover:bg-black/10 px-2 py-1 rounded flex items-center">
            View
            <ChevronDownIcon className="h-4 w-4 ml-1" />
          </button>
          <button className="hover:bg-black/10 px-2 py-1 rounded">Help</button>
        </div>
        
        {/* Mobile menu */}
        {isMobile && (
          <HeaderMenu isMobile={true} />
        )}
      </div>

      {/* Right side - Search, Actions, and User Profile */}
      <div className="flex items-center space-x-2 md:space-x-5">
        {/* Mobile Search Button */}
        {isMobile && !isSearchOpen && (
          <button 
            onClick={() => setIsSearchOpen(true)}
            className="p-2 rounded-full text-white hover:bg-black/10 focus:outline-none"
            aria-label="Open search"
          >
            <MagnifyingGlassIcon className="h-5 w-5" />
          </button>
        )}
        
        {/* Expanded Mobile Search */}
        {isMobile && isSearchOpen ? (
          <div className="absolute inset-x-0 top-0 bg-[#2A6E78] h-16 z-50 px-2 flex items-center">
            <form onSubmit={handleSearch} className="flex-1 flex items-center">
              <Input
                type="text"
                placeholder="Search..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="flex-1 bg-white/90 border-transparent focus:border-white"
                autoFocus
              />
              <Button type="submit" variant="ghost" className="ml-2 text-white">
                <MagnifyingGlassIcon className="h-5 w-5" />
              </Button>
              <Button 
                type="button" 
                variant="ghost" 
                className="ml-1 text-white" 
                onClick={() => setIsSearchOpen(false)}
              >
                <XMarkIcon className="h-5 w-5" />
              </Button>
            </form>
          </div>
        ) : (
          // Desktop Search Bar
          <div className="relative hidden md:block">
            <form onSubmit={handleSearch}>
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-500" />
              <input 
                type="text" 
                placeholder="Quick Search" 
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-64 pl-10 pr-4 py-2 text-sm bg-white border border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-cyan-500"
              />
            </form>
          </div>
        )}

        {/* Action Icons */}
        <div className="flex items-center space-x-1 md:space-x-3 text-white">
          {/* Notifications */}
          <button className="relative p-2 rounded-full hover:bg-black/10">
            <BellIcon className="h-5 w-5" />
            <span className="absolute top-1 right-1 h-2 w-2 bg-red-500 rounded-full"></span>
          </button>
          {/* Messages */}
          <button className="p-2 rounded-full hover:bg-black/10">
            <EnvelopeIcon className="h-5 w-5" />
          </button>
          {/* Document/Create */}
          <button className="p-2 rounded-full hover:bg-black/10">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
              <path fillRule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
            </svg>
          </button>
          {/* Print */}
          <button className="p-2 rounded-full hover:bg-black/10">
            <PrinterIcon className="h-5 w-5" />
          </button>
          {/* Settings */}
          <button className="p-2 rounded-full hover:bg-black/10">
            <Cog6ToothIcon className="h-5 w-5" />
          </button>
        </div>

        {/* User Profile */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="relative h-8 w-8 rounded-full hover:bg-black/10 focus-visible:ring-white">
              <Avatar className="h-8 w-8">
                <AvatarImage src={user?.avatar || undefined} alt={fullName} />
                <AvatarFallback>{avatarFallback}</AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56" align="end" forceMount>
            <DropdownMenuLabel className="font-normal">
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium leading-none">{fullName}</p>
                <p className="text-xs leading-none text-muted-foreground">
                  {user?.email}
                </p>
                <p className="text-sm font-medium text-gray-500 truncate dark:text-gray-400">
                  {displayRole}
                </p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem>
                Profile
              </DropdownMenuItem>
              <DropdownMenuItem>
                Billing
              </DropdownMenuItem>
              <DropdownMenuItem>
                Settings
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
              <div className="flex items-center justify-between w-full">
                <Label htmlFor="theme-switcher" className="flex items-center gap-2 font-normal cursor-pointer">
                  {theme === 'light' ? <SunIcon className="h-4 w-4" /> : <MoonIcon className="h-4 w-4" />}
                  <span>{theme === 'light' ? 'Light Mode' : 'Dark Mode'}</span>
                </Label>
                <Switch
                  id="theme-switcher"
                  checked={theme === 'dark'}
                  onCheckedChange={toggleTheme}
                />
              </div>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={logout}>
              <ArrowRightOnRectangleIcon className="mr-2 h-4 w-4" />
              <span>Log out</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
};

export default AppHeader;

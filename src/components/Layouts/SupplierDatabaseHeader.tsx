'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useTheme } from 'next-themes';
import { FiSearch, FiBell, FiMessageCircle, FiSave, FiSettings, FiLogOut, FiSun, FiMoon, FiX, FiMenu, FiFilter, FiChevronDown, FiUsers, FiPlus, FiDownload, FiUpload, FiArrowLeft } from 'react-icons/fi';
import { useAuth } from '@/context/AuthContext';
import { useSupplierDatabase } from '@/context/SupplierDatabaseContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuGroup, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface SupplierDatabaseHeaderProps {
  toggleSidebar: () => void;
  isSidebarOpen: boolean;
  toggleMobileSidebar: () => void;
}

const SupplierDatabaseHeader = ({ 
  toggleSidebar, 
  isSidebarOpen, 
  toggleMobileSidebar 
}: SupplierDatabaseHeaderProps) => {
  const { user, logout } = useAuth();
  const { theme, setTheme } = useTheme();
  const { activeTab, setActiveTab, searchTerm, setSearchTerm } = useSupplierDatabase();
  const [isMobile, setIsMobile] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  // Check if we're on mobile on initial render and when window resizes
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    // Initial check
    checkIfMobile();
    
    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Searching for:', searchTerm);
    // Implement search functionality
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  return (
    <header className="bg-[#18546c] text-white">
      {/* Top header with controls */}
      <div className="h-16 flex items-center justify-between px-4 md:px-6">
        {/* Left side - Toggle buttons and title */}
        <div className="flex items-center space-x-4">
          {/* Mobile sidebar toggle */}
          <button
            onClick={toggleMobileSidebar}
            className="md:hidden p-2 rounded-full hover:bg-black/10 focus:outline-none"
            aria-label="Toggle mobile sidebar"
          >
            <FiMenu className="h-5 w-5" />
          </button>
          
          {/* Desktop sidebar toggle */}
          <button
            onClick={toggleSidebar}
            className="hidden md:block p-2 rounded-full hover:bg-black/10 focus:outline-none"
            aria-label="Toggle sidebar"
          >
            <FiMenu className="h-5 w-5" />
          </button>
          
          <div className="flex items-center space-x-2 md:space-x-4 overflow-x-auto">
            <Link 
              href="/supplier-database"
              className={`text-white hover:bg-white/10 px-3 py-1 text-sm font-medium transition-colors whitespace-nowrap ${activeTab === 'All suppliers' ? 'bg-white/10' : ''}`}
              onClick={() => setActiveTab('All suppliers')}
            >
              All suppliers
            </Link>
            <Link 
              href="/supplier-database?tab=active"
              className={`text-white hover:bg-white/10 px-3 py-1 text-sm font-medium transition-colors whitespace-nowrap ${activeTab === 'Active' ? 'bg-white/10' : ''}`}
              onClick={() => setActiveTab('Active')}
            >
              Active
            </Link>
            <Link 
              href="/supplier-database?tab=deactivated"
              className={`text-white hover:bg-white/10 px-3 py-1 text-sm font-medium transition-colors whitespace-nowrap ${activeTab === 'Deactivated' ? 'bg-white/10' : ''}`}
              onClick={() => setActiveTab('Deactivated')}
            >
              Deactivated
            </Link>
            <Link 
              href="/supplier-database?tab=blacklisted"
              className={`text-white hover:bg-white/10 px-3 py-1 text-sm font-medium transition-colors whitespace-nowrap ${activeTab === 'Blacklisted' ? 'bg-white/10' : ''}`}
              onClick={() => setActiveTab('Blacklisted')}
            >
              Blacklisted
            </Link>
            <Link 
              href="/supplier-database?tab=help"
              className={`text-white hover:bg-white/10 px-3 py-1 text-sm font-medium transition-colors whitespace-nowrap ${activeTab === 'Help' ? 'bg-white/10' : ''}`}
              onClick={() => setActiveTab('Help')}
            >
              Help
            </Link>
          </div>
        </div>
        
        {/* Right side - Search, Actions, and User Profile */}
        <div className="flex items-center space-x-2 md:space-x-4">
          {/* Mobile Search Button */}
          {isMobile && !isSearchOpen && (
            <button 
              onClick={() => setIsSearchOpen(true)}
              className="p-2 rounded-full hover:bg-black/10 focus:outline-none"
              aria-label="Open search"
            >
              <FiSearch className="h-5 w-5" />
            </button>
          )}
          
          {/* Expanded Mobile Search */}
          {isMobile && isSearchOpen ? (
            <div className="absolute inset-x-0 top-0 bg-[#2A6E78] h-16 z-50 px-4 flex items-center">
              <form onSubmit={handleSearch} className="flex-1 flex items-center">
                <Input
                  type="text"
                  placeholder="Search suppliers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="flex-1 bg-white/90 border-transparent focus:border-white"
                  autoFocus
                />
                <Button type="submit" variant="ghost" className="ml-2 text-white">
                  <FiSearch className="h-5 w-5" />
                </Button>
                <Button 
                  type="button" 
                  variant="ghost" 
                  className="ml-1 text-white" 
                  onClick={() => setIsSearchOpen(false)}
                >
                  <FiX className="h-5 w-5" />
                </Button>
              </form>
            </div>
          ) : (
            // Desktop Search Bar
            <div className="relative hidden md:block">
              <form onSubmit={handleSearch}>
                <div className="relative">
                  <FiSearch className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-500" />
                  <input 
                    type="text" 
                    placeholder="Search suppliers..." 
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-64 pl-10 pr-4 py-2 text-sm bg-white border border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-cyan-500 text-gray-900"
                  />
                </div>
              </form>
            </div>
          )}
          
          {/* Filter Button (Mobile only) */}
          {isMobile && (
            <button 
              onClick={toggleMobileSidebar}
              className="p-2 rounded-full hover:bg-black/10 focus:outline-none"
              aria-label="Show filters"
            >
              <FiFilter className="h-5 w-5" />
            </button>
          )}

          {/* Action Icons - Show fewer on mobile */}
          <div className="flex items-center space-x-1 md:space-x-3">
            <button className="p-2 rounded-full hover:bg-black/10 focus:outline-none hidden md:block">
              <FiBell className="h-5 w-5" />
            </button>
            <button className="p-2 rounded-full hover:bg-black/10 focus:outline-none hidden md:block">
              <FiMessageCircle className="h-5 w-5" />
            </button>
            <button className="p-2 rounded-full hover:bg-black/10 focus:outline-none">
              <FiSave className="h-5 w-5" />
            </button>
          </div>

          {/* User Profile Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button className="flex items-center space-x-2 focus:outline-none">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user?.avatar || ''} alt={`${user?.firstName || ''} ${user?.lastName || ''}`} />
                  <AvatarFallback>{user?.firstName?.[0] || 'U'}</AvatarFallback>
                </Avatar>
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                <DropdownMenuItem>
                  <FiSettings className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center">
                      {theme === 'dark' ? (
                        <FiMoon className="mr-2 h-4 w-4" />
                      ) : (
                        <FiSun className="mr-2 h-4 w-4" />
                      )}
                      <span>Dark Mode</span>
                    </div>
                    <Switch
                      checked={theme === 'dark'}
                      onCheckedChange={(checked) => setTheme(checked ? 'dark' : 'light')}
                    />
                  </div>
                </DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={logout}>
                <FiLogOut className="mr-2 h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      
      {/* Breadcrumb removed - now using SupplierDatabaseBreadcrumb component */}
    </header>
  );
};

export default SupplierDatabaseHeader;

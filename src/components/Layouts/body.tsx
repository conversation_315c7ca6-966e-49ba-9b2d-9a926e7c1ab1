'use client';

import { useEffect } from 'react';

export function Body({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // This effect only runs on the client side
    const body = document.body;
    if (body) {
      body.classList.add('clickup-chrome-ext_installed');
      body.setAttribute('data-new-gr-c-s-check-loaded', '14.1237.0');
      body.setAttribute('data-gr-ext-installed', '');
    }
  }, []);

  return <>{children}</>;
}

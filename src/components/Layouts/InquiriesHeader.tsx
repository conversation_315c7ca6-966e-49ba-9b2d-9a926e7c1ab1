import React from 'react';
import { FiMenu, FiSearch, FiBell, FiMessageSquare, FiCheckSquare, FiFolder, FiSettings } from 'react-icons/fi';
import { FiChevronDown } from 'react-icons/fi';

interface InquiriesHeaderProps {
  onToggleSidebar: () => void;
  onToggleMobileSidebar: () => void;
  isSidebarOpen: boolean;
}

export default function InquiriesHeader({ 
  onToggleSidebar, 
  onToggleMobileSidebar,
  isSidebarOpen 
}: InquiriesHeaderProps) {
  return (
    <header className="bg-white dark:bg-slate-800 shadow-sm">
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Left section */}
          <div className="flex items-center">
            {/* Mobile menu button */}
            <button
              type="button"
              className="md:hidden text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300"
              onClick={onToggleMobileSidebar}
            >
              <span className="sr-only">Open sidebar</span>
              <FiMenu className="h-6 w-6" />
            </button>

            {/* Desktop sidebar toggle */}
            <button
              type="button"
              className="hidden md:block text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300"
              onClick={onToggleSidebar}
            >
              <span className="sr-only">{isSidebarOpen ? 'Collapse' : 'Expand'} sidebar</span>
              <FiMenu className="h-6 w-6" />
            </button>

            {/* Logo */}
            <div className="ml-4 flex items-center">
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">ASCENSION</h1>
            </div>
          </div>


          {/* Search bar */}
          <div className="flex-1 max-w-2xl px-4 mx  -4">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FiSearch className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-slate-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Ouros Search"
              />
            </div>
          </div>

          {/* Right section */}
          <div className="flex items-center space-x-4">
            <button className="text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300">
              <span className="sr-only">Notifications</span>
              <FiBell className="h-6 w-6" />
            </button>
            <button className="text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300">
              <span className="sr-only">Messages</span>
              <FiMessageSquare className="h-6 w-6" />
            </button>
            <button className="text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300">
              <span className="sr-only">Tasks</span>
              <FiCheckSquare className="h-6 w-6" />
            </button>
            <button className="text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300">
              <span className="sr-only">Files</span>
              <FiFolder className="h-6 w-6" />
            </button>
            <button className="text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300">
              <span className="sr-only">Settings</span>
              <FiSettings className="h-6 w-6" />
            </button>
            
            {/* Language selector */}
            <div className="relative">
              <button className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white">
                EN
                <FiChevronDown className="ml-1 h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}

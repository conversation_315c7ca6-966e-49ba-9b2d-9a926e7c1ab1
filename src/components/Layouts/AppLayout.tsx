import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface AppLayoutProps {
  children: ReactNode;
  className?: string;
}

export function AppLayout({ children, className }: AppLayoutProps) {
  return (
    <div className={cn(
      'flex min-h-screen flex-col bg-gradient-to-br from-gray-25 to-gray-50 dark:from-gray-900 dark:to-gray-950',
      'transition-colors duration-200',
      'relative overflow-hidden',
      className
    )}>
      {/* Subtle background pattern */}
      <div className="absolute inset-0 -z-0 opacity-[0.03] dark:opacity-[0.05]" 
           style={{
             backgroundImage: 'radial-gradient(#64748b 1px, transparent 1px)',
             backgroundSize: '16px 16px'
           }} 
      />
      <div className="relative z-10 flex-1 flex flex-col">
        {children}
      </div>
    </div>
  );
}

AppLayout.Content = function AppLayoutContent({ 
  children, 
  className 
}: { 
  children: ReactNode; 
  className?: string;
}) {
  return (
    <main className={cn(
      'flex-1 flex flex-col overflow-hidden',
      'transition-all duration-300 ease-in-out',
      className
    )}>
      {children}
    </main>
  );
};

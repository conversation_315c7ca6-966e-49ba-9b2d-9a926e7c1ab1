'use client';

import { useEffect, useState } from 'react';
import { ThemeProvider as NextThemesProvider, useTheme as useNextTheme } from 'next-themes';

// This component wraps the app and prevents hydration issues
// by only rendering the theme provider after mounting on the client
function ThemeProviderWrapper({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render until we're on the client to prevent hydration mismatch
  if (!mounted) {
    return (
      <div style={{ visibility: 'hidden' }}>{children}</div>
    );
  }


  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme="light"
      enableSystem={false}
      storageKey="ascension-theme"
      disableTransitionOnChange={false}
    >
      <ThemeUpdater />
      {children}
    </NextThemesProvider>
  );
}

// Component to handle theme changes and apply smooth transitions
function ThemeUpdater() {
  const { theme } = useNextTheme();

  useEffect(() => {
    // Skip if running on server
    if (typeof document === 'undefined') return;

    const root = document.documentElement;
    
    // Set initial transition
    root.style.transition = 'background-color 200ms ease, color 200ms ease';
    
    // Clean up
    return () => {
      root.style.transition = '';
    };
  }, [theme]);

  return null;
}

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProviderWrapper>
      {children}
    </ThemeProviderWrapper>
  );
}

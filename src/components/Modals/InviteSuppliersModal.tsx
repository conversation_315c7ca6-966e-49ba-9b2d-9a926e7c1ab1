'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { FiUsers, FiX, FiPlus } from 'react-icons/fi';
import { Badge } from '@/components/ui/badge';
import { api } from '@/services/api';

interface InviteSuppliersModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const InviteSuppliersModal: React.FC<InviteSuppliersModalProps> = ({ isOpen, onClose }) => {
  const [emailInput, setEmailInput] = useState('');
  const [emailList, setEmailList] = useState<string[]>([]);
  const [isSending, setIsSending] = useState(false);
  const [error, setError] = useState('');

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setEmailInput('');
      setEmailList([]);
      setError('');
    }
  }, [isOpen]);

  const validateEmail = (email: string) => {
    const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return re.test(email);
  };

  const handleAddEmail = () => {
    const trimmedEmail = emailInput.trim();
    
    if (!trimmedEmail) {
      return;
    }
    
    // Check if input contains multiple emails separated by commas
    if (trimmedEmail.includes(',')) {
      const multipleEmails = trimmedEmail.split(',').map(e => e.trim()).filter(e => e);
      
      // Validate each email
      const validEmails: string[] = [];
      const invalidEmails: string[] = [];
      
      multipleEmails.forEach(email => {
        if (validateEmail(email) && !emailList.includes(email)) {
          validEmails.push(email);
        } else if (email) {
          invalidEmails.push(email);
        }
      });
      
      if (invalidEmails.length > 0) {
        setError(`Invalid email(s): ${invalidEmails.join(', ')}`);
      } else {
        setError('');
      }
      
      setEmailList(prev => [...prev, ...validEmails]);
    } else {
      // Single email validation
      if (validateEmail(trimmedEmail) && !emailList.includes(trimmedEmail)) {
        setEmailList(prev => [...prev, trimmedEmail]);
        setError('');
      } else if (emailList.includes(trimmedEmail)) {
        setError('Email already added');
      } else {
        setError('Please enter a valid email address');
      }
    }
    
    setEmailInput('');
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddEmail();
    } else if (e.key === ',' || e.key === ' ') {
      if (emailInput.trim()) {
        e.preventDefault();
        handleAddEmail();
      }
    }
  };

  const handleRemoveEmail = (emailToRemove: string) => {
    setEmailList(emailList.filter(email => email !== emailToRemove));
  };

  const handleSendInvitation = async () => {
    // Set loading state
    setIsSending(true);
    
    try {
      // Send invitations directly using the API service
      // According to the API documentation, we don't need to check for template existence first
      // The API will handle sending the invitation emails
      
      // Send bulk invitations with explicit supplier role
      const result = await api.sendBulkInvitations(
        emailList,
        'supplier', // Explicitly set role to supplier
        'You have been invited to join our supplier network.'
      );
      
      // Process results
      if (!result?.data) {
        throw new Error('Failed to send invitations: No response data');
      }
      const { successful, failed, results } = result.data;
      
      // Reset states
      setIsSending(false);
      
      if (failed > 0) {
        // Some invitations failed
        const failedEmails = results
          .filter(r => !r.success)
          .map(r => `${r.email}: ${r.error || 'Unknown error'}`)
          .join('\n');
          
        alert(`${successful} invitation(s) sent successfully.\n${failed} invitation(s) failed:\n${failedEmails}`);
      } else {
        // All invitations sent successfully
        alert(`Invitations sent successfully to ${successful} supplier(s)!`);
        // Clear the form only on success
        setEmailList([]);
        setEmailInput('');
        onClose();
      }
    } catch (error: any) {
      console.error('Error sending invitations:', error);
      alert(`Error sending invitations: ${error.message || 'Unknown error'}`);
      setIsSending(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] p-0 overflow-hidden rounded-xl shadow-xl">
        <DialogHeader className="text-center bg-gradient-to-r from-[#18546c] to-[#1a6985] text-white p-6">
          <div className="inline-flex justify-center items-center mb-4 bg-white/20 backdrop-blur-sm p-3 rounded-full shadow-lg">
            <FiUsers className="h-5 w-5 text-white" />
          </div>
          <DialogTitle className="text-2xl font-bold mb-2">Invite New Suppliers</DialogTitle>
          <DialogDescription className="text-white/90 text-base">
            Expand your supplier network
          </DialogDescription>
        </DialogHeader>
        
        <div className="p-6 space-y-4">
            <div className="grid w-full items-center gap-3">
              <Label htmlFor="emails" className="text-left font-medium text-gray-700">
                Supplier Email Addresses
              </Label>
              
              {/* Email input with add button */}
              <div className="flex gap-2">
                <Input
                  id="emails"
                  type="email"
                  placeholder="<EMAIL>"
                  value={emailInput}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEmailInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  className="flex-1 h-10 px-3 text-sm rounded-md border-gray-300 focus:border-[#18546c] focus:ring-[#18546c] transition-all duration-200"
                />
                <Button 
                  type="button" 
                  onClick={handleAddEmail}
                  className="bg-[#18546c] hover:bg-[#1a6985] text-white h-10 px-3 rounded-md transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <FiPlus className="h-4 w-4" />
                </Button>
              </div>
              
              {/* Error message */}
              {error && (
                <p className="text-sm text-red-500 font-medium flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                  </svg>
                  {error}
                </p>
              )}
              
              {/* Email tags */}
              {emailList.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-3 p-3 bg-gray-50 rounded-md border border-gray-100 max-h-[120px] overflow-y-auto">
                  {emailList.map((email, index) => (
                    <Badge 
                      key={index} 
                      className="bg-[#e8f0f2] text-[#18546c] hover:bg-[#d8e8ec] px-2 py-1 flex items-center gap-1 text-xs rounded-full transition-all duration-200"
                    >
                      {email}
                      <button 
                        type="button" 
                        onClick={() => handleRemoveEmail(email)}
                        className="ml-1 text-[#18546c] hover:text-red-500 focus:outline-none rounded-full hover:bg-white/50 p-1 transition-all duration-200"
                      >
                        <FiX className="h-4 w-4" />
                        <span className="sr-only">Remove</span>
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
              
              <p className="text-xs text-gray-500 mt-1 flex items-center gap-1">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Press Enter or comma to add multiple emails
              </p>
            </div>
        </div>
        
        <DialogFooter className="bg-gray-50 p-4 flex justify-end gap-2 border-t">
          <Button 
            variant="outline" 
            onClick={onClose} 
            className="border border-gray-300 bg-white text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-all duration-200 text-sm font-medium py-1 px-4 h-9 rounded-md"
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSendInvitation} 
            disabled={emailList.length === 0 || isSending} 
            className={`bg-[#18546c] text-white hover:bg-[#1a6985] transition-all duration-200 shadow-sm hover:shadow-md text-sm font-medium py-1 px-4 h-9 rounded-md ${emailList.length === 0 || isSending ? 'opacity-70 cursor-not-allowed' : ''}`}
          >
            {isSending ? (
              <span className="flex items-center gap-2">
                <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Sending...
              </span>
            ) : (
              <span className="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
                {`Send ${emailList.length > 0 ? `${emailList.length} ` : ''}Invitation${emailList.length !== 1 ? 's' : ''}`}
              </span>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default InviteSuppliersModal;

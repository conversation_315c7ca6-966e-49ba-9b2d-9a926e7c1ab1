"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useF<PERSON>, ControllerRenderProps } from "react-hook-form";
import * as z from "zod";
import { useRouter } from 'next/navigation';

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "./form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  createEmailTemplate, 
  updateEmailTemplate, 
  ApiEmailTemplate, 
  generateSlug, 
  validateSlug 
} from "@/services/emailTemplates";
import {
  welcomeTemplate,
  supplierInvitationTemplate,
  passwordResetTemplate,
  TEMPLATE_NAMES,
  TemplateName,
  EmailTemplate,
} from "@/email-templates";

const formSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  subject: z.string().min(5, { message: "Subject must be at least 5 characters." }),
  slug: z.string().refine(validateSlug, { message: "Slug must be in a valid format (e.g., 'my-template-slug')." }),
  body: z.string().min(10, { message: "Body must not be empty." }),
  is_html: z.boolean().default(true),
  signature: z.string().optional(),
  variables: z.string().optional(),
  isActive: z.boolean().default(true),
});

type EmailTemplateFormValues = z.infer<typeof formSchema>;

interface EmailTemplateFormProps {
  initialData?: ApiEmailTemplate;
}

export function EmailTemplateForm({ initialData }: EmailTemplateFormProps) {
  const router = useRouter();

  const form = useForm<EmailTemplateFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData ? {
      ...initialData,
      body: initialData.html, // Map html to body for editing
      is_html: true, // Assume existing templates are HTML
      signature: '', // Add signature field
      variables: initialData.variables.join(', '),
    } : {
      name: "",
      subject: "",
      slug: "",
      body: "",
      is_html: true,
      signature: "",
      variables: "",
      isActive: true,
    },
  });

  const handleTemplateSelect = (templateName: TemplateName) => {
    if (!templateName) return;

    let template: EmailTemplate;
    const defaultName = templateName
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    switch (templateName) {
      case TEMPLATE_NAMES.WELCOME:
        template = welcomeTemplate({ 
          userName: '{{userName}}', 
          loginUrl: '{{loginUrl}}',
          supportEmail: '{{supportEmail}}',
          helpUrl: '{{helpUrl}}',
        });
        form.setValue("variables", 'userName, loginUrl, supportEmail, helpUrl');
        break;
      case TEMPLATE_NAMES.SUPPLIER_INVITATION:
        template = supplierInvitationTemplate({ 
          invitationId: '{{invitationId}}',
          organizationName: '{{organizationName}}',
          role: '{{role}}',
          expiryDate: '{{expiryDate}}',
          invitationUrl: '{{invitationUrl}}',
        });
        form.setValue("variables", 'invitationId, organizationName, role, expiryDate, invitationUrl');
        break;
      case TEMPLATE_NAMES.PASSWORD_RESET:
        template = passwordResetTemplate({ 
          resetUrl: '{{resetUrl}}',
          expiresIn: '{{expiresIn}}',
          userEmail: '{{userEmail}}',
        });
        form.setValue("variables", 'resetUrl, expiresIn, userEmail');
        break;
      default:
        return;
    }

    form.setValue("name", defaultName);
    form.setValue("subject", template.subject);
    form.setValue("body", template.html);
    form.setValue("is_html", true);
  };

  const handleSubjectChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    form.setValue("subject", e.target.value);
    if (!form.getValues("slug") || !initialData) { // Only auto-generate slug if it's empty or on create
      form.setValue("slug", generateSlug(e.target.value));
    }
  };

  const onSubmit = async (data: EmailTemplateFormValues) => {
    try {
      const payload = {
        ...data,
        variables: data.variables ? data.variables.split(',').map(v => v.trim()) : [],
        // Map form fields to the expected DTO structure
        html: data.is_html ? data.body : '',
        text: !data.is_html ? data.body : '',
      };

      if (initialData) {
        await updateEmailTemplate(initialData.slug, payload);
        toast.success(`Template "${data.name}" has been updated successfully.`);
      } else {
        await createEmailTemplate(payload);
        toast.success("Email template has been created successfully.");
      }

      router.push('/dashboard/email-templates');
      router.refresh();

    } catch (error: any) {
      const action = initialData ? 'update' : 'create';
      console.error(`Failed to ${action} template:`, error);
      const errorMessage = error.response?.data?.message || `Could not ${action} the template. Please try again.`;
      toast.error(errorMessage);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        {!initialData && (
          <FormItem>
            <FormLabel>Start with a Pre-designed Template</FormLabel>
            <Select onValueChange={(value: TemplateName) => handleTemplateSelect(value)}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select a template to get started..." />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value={TEMPLATE_NAMES.WELCOME}>Welcome</SelectItem>
                <SelectItem value={TEMPLATE_NAMES.SUPPLIER_INVITATION}>Supplier Invitation</SelectItem>
                <SelectItem value={TEMPLATE_NAMES.PASSWORD_RESET}>Password Reset</SelectItem>
              </SelectContent>
            </Select>
            <FormDescription>
              This will populate the form with a pre-designed template.
            </FormDescription>
          </FormItem>
        )}
        <FormField
          control={form.control}
          name="name"
          render={({ field }: { field: ControllerRenderProps<EmailTemplateFormValues, 'name'> }) => (
            <FormItem>
              <FormLabel>Template Name</FormLabel>
              <FormControl>
                <Input placeholder="e.g., Welcome Email" {...field} />
              </FormControl>
              <FormDescription>A descriptive name for internal use.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="subject"
          render={({ field }: { field: ControllerRenderProps<EmailTemplateFormValues, 'subject'> }) => (
            <FormItem>
              <FormLabel>Email Subject</FormLabel>
              <FormControl>
                <Input placeholder="e.g., Welcome to Our Service!" {...field} onChange={handleSubjectChange} />
              </FormControl>
              <FormDescription>The subject line of the email.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="slug"
          render={({ field }: { field: ControllerRenderProps<EmailTemplateFormValues, 'slug'> }) => (
            <FormItem>
              <FormLabel>Slug</FormLabel>
              <FormControl>
                <Input placeholder="e.g., welcome-email" {...field} />
              </FormControl>
              <FormDescription>A unique identifier for the template. Auto-generated from the subject.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="body"
          render={({ field }: { field: ControllerRenderProps<EmailTemplateFormValues, 'body'> }) => (
            <FormItem>
              <FormLabel>Email Body</FormLabel>
              <FormControl>
                <Textarea placeholder="Enter the email body here..." {...field} rows={15} />
              </FormControl>
              <FormDescription>The content of the email. Use {'{{variable_name}}'} for placeholders.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="is_html"
          render={({ field }: { field: ControllerRenderProps<EmailTemplateFormValues, 'is_html'> }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel>HTML Email</FormLabel>
                <FormDescription>
                  Is this an HTML email? If not, it will be treated as plain text.
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="signature"
          render={({ field }: { field: ControllerRenderProps<EmailTemplateFormValues, 'signature'> }) => (
            <FormItem>
              <FormLabel>Signature (Optional)</FormLabel>
              <FormControl>
                <Input placeholder="e.g., The Ascension Team" {...field} />
              </FormControl>
              <FormDescription>An optional signature to append to the email.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="variables"
          render={({ field }: { field: ControllerRenderProps<EmailTemplateFormValues, 'variables'> }) => (
            <FormItem>
              <FormLabel>Variables (Optional)</FormLabel>
              <FormControl>
                <Input placeholder="e.g., name, order_id, link" {...field} />
              </FormControl>
              <FormDescription>A comma-separated list of variables used in the template (e.g., name, order_id).</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="isActive"
          render={({ field }: { field: ControllerRenderProps<EmailTemplateFormValues, 'isActive'> }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel>Active</FormLabel>
                <FormDescription>
                  Whether the template is currently active and can be used for sending emails.
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <Button type="submit" disabled={form.formState.isSubmitting}>
          {initialData ? 'Update Template' : 'Create Template'}
        </Button>
      </form>
    </Form>
  );
}

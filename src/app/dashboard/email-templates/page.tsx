"use client";

import { useEffect, useState } from 'react';
import { PlusCircle } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { listEmailTemplates, ApiEmailTemplate } from '@/services/emailTemplates';
import { columns } from './components/columns';
import { DataTable } from './components/data-table';

export default function EmailTemplatesPage() {
  const [templates, setTemplates] = useState<ApiEmailTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        setLoading(true);
        const response = await listEmailTemplates();
        setTemplates(response.items);
        setError(null);
      } catch (err) {
        setError('Failed to fetch email templates. Please try again later.');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchTemplates();
  }, []);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Email Templates</CardTitle>
            <CardDescription>Manage email templates for your organization.</CardDescription>
          </div>
          <Link href="/dashboard/email-templates/new">
            <Button>
              <PlusCircle className="mr-2 h-4 w-4" /> Create Template
            </Button>
          </Link>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <p>Loading...</p>
        ) : error ? (
          <p className="text-red-500">{error}</p>
        ) : (
          <DataTable columns={columns} data={templates} />
        )}
      </CardContent>
    </Card>
  );
}

import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { HomeIcon } from '@heroicons/react/24/solid';
import { ChevronRightIcon } from '@heroicons/react/24/solid';

export function SupplierDatabaseBreadcrumb({ className }: { className?: string }) {
  const pathname = usePathname();
  const segments = pathname?.split('/').filter(Boolean) || [];

  // Create a mapping for prettier display names
  const displayNames: Record<string, string> = {
    'supplier-database': 'Supplier Database',
    'dashboard': 'Dashboard',
    'pre-qualified': 'Pre-qualified',
  };

  if (segments.length === 0) return null;

  return (
    <nav className={cn('mb-4 flex', className)} aria-label="Breadcrumb">
      <ol className="inline-flex items-center space-x-1 md:space-x-2">
        <li className="inline-flex items-center">
          <Link
            href="/dashboard"
            className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-[#18546c] dark:text-gray-400 dark:hover:text-white transition-colors"
          >
            <HomeIcon className="mr-2 h-4 w-4" />
            Home
          </Link>
        </li>
        {segments.map((segment, index) => {
          const href = `/${segments.slice(0, index + 1).join('/')}`;
          const isLast = index === segments.length - 1;
          // Use the display name mapping or capitalize the first letter
          const segmentName = displayNames[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);

          return (
            <li key={segment}>
              <div className="flex items-center">
                <ChevronRightIcon className="mx-1 h-3 w-3 text-gray-500 dark:text-gray-400" />
                {isLast ? (
                  <span className="ml-1 text-sm font-medium text-[#18546c] dark:text-gray-300 md:ml-1">
                    {segmentName}
                  </span>
                ) : (
                  <Link
                    href={href}
                    className="ml-1 text-sm font-medium text-gray-700 hover:text-[#18546c] dark:text-gray-400 dark:hover:text-white transition-colors md:ml-1"
                  >
                    {segmentName}
                  </Link>
                )}
              </div>
            </li>
          );
        })}
      </ol>
    </nav>
  );
}

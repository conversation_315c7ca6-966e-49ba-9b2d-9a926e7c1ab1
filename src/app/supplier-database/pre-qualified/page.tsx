'use client';

import React from 'react';
import { AppLayout } from '@/components/Layouts/AppLayout';
import AppHeader from '@/components/Layouts/AppHeader';
import AppSidebar from '@/components/Layouts/AppSidebar';

export default function PreQualifiedSuppliersPage() {
  return (
    <AppLayout>
      <AppHeader />
      <div className="flex flex-1 overflow-hidden">
        <AppSidebar />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="mb-6">
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Pre-Qualified Suppliers</h1>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <p className="text-gray-600 dark:text-gray-300">Pre-qualified suppliers content will go here.</p>
          </div>
        </main>
      </div>
    </AppLayout>
  );
}

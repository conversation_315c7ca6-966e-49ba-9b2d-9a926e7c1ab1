'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/Card';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { FiSearch, FiFilter, FiPlus, FiRefreshCw, FiDownload } from 'react-icons/fi';
import { format } from 'date-fns';
import { api } from '@/services/api';
import InviteSuppliersModal from '@/components/Modals/InviteSuppliersModal';

// Define invitation type
type Invitation = {
  id: string;
  email: string;
  role: string;
  status: string;
  organization_id: string;
  created_at: string;
  expires_at: string;
  accepted_at?: string;
};

// Fallback mock data for invitations (will be replaced by API data)
const mockInvitations = [
  {
    id: '1',
    email: '<EMAIL>',
    company: 'ABC Supplies Ltd',
    status: 'Pending',
    sentDate: new Date(2025, 6, 1),
    expiryDate: new Date(2025, 7, 1),
  },
  {
    id: '2',
    email: '<EMAIL>',
    company: 'XYZ Manufacturing',
    status: 'Accepted',
    sentDate: new Date(2025, 6, 1),
    expiryDate: new Date(2025, 7, 1),
    acceptedDate: new Date(2025, 6, 3),
  },
  {
    id: '3',
    email: '<EMAIL>',
    company: 'Global Logistics Inc',
    status: 'Expired',
    sentDate: new Date(2025, 5, 1),
    expiryDate: new Date(2025, 6, 1),
  },
  {
    id: '4',
    email: '<EMAIL>',
    company: 'Tech Solutions Co',
    status: 'Pending',
    sentDate: new Date(2025, 6, 2),
    expiryDate: new Date(2025, 7, 2),
  },
  {
    id: '5',
    email: '<EMAIL>',
    company: 'Eco Friendly Products',
    status: 'Accepted',
    sentDate: new Date(2025, 6, 1),
    expiryDate: new Date(2025, 7, 1),
    acceptedDate: new Date(2025, 6, 5),
  },
];

export default function InvitationsPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState('All');
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  
  // Fetch invitations from API
  useEffect(() => {
    const fetchInvitations = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Convert filter to API status parameter
        const statusParam = filter !== 'All' ? filter.toLowerCase() : undefined;
        
        const response = await api.getInvitations({
          status: statusParam,
          page,
          limit
        });
        
        if (response.status === 'success' && response.data) {
          setInvitations(response.data.items);
          setTotalPages(response.data.total_pages);
        } else {
          throw new Error('Failed to fetch invitations');
        }
      } catch (err: any) {
        console.error('Error fetching invitations:', err);
        setError(err.message || 'An error occurred while fetching invitations');
        // Use mock data as fallback
        setInvitations(mockInvitations.map(inv => ({
          id: inv.id,
          email: inv.email,
          role: 'supplier',
          status: inv.status.toLowerCase(),
          organization_id: '1',
          created_at: inv.sentDate.toISOString(),
          expires_at: inv.expiryDate.toISOString(),
          accepted_at: inv.acceptedDate?.toISOString()
        })));
      } finally {
        setLoading(false);
      }
    };
    
    fetchInvitations();
  }, [filter, page, limit]);
  
  // Refresh invitations
  const handleRefresh = () => {
    setPage(1);
    // The useEffect will trigger a new fetch
  };
  
  // Filter invitations based on search term
  const filteredInvitations = invitations.filter(invitation => {
    const matchesSearch = 
      invitation.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesSearch;
  });
  
  // Function to get appropriate badge color based on status
  const getStatusBadgeColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'accepted':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'expired':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
    }
  };
  
  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (error) {
      return 'Invalid date';
    }
  };
  
  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Supplier Invitations</h1>
          <p className="text-gray-500 dark:text-gray-400">Manage and track all supplier invitations</p>
        </div>
        <div className="flex gap-2">
          <Button 
            onClick={() => router.push('/supplier-database')}
            variant="outline"
          >
            Back to Suppliers
          </Button>
          <Button
            onClick={handleRefresh}
            variant="outline"
            disabled={loading}
          >
            {loading ? (
              <>
                <span className="animate-spin mr-2">⟳</span>
                Loading...
              </>
            ) : (
              <>
                <FiRefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </>
            )}
          </Button>
          <Button
            onClick={() => {}}
            variant="outline"
          >
            <FiDownload className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button
            onClick={() => setIsInviteModalOpen(true)}
            variant="default"
          >
            <FiPlus className="mr-2 h-4 w-4" />
            Invite Supplier
          </Button>
        </div>
      </div>
      
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
            placeholder="Search by email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          <Button
            variant={filter === 'All' ? 'default' : 'outline'}
            onClick={() => setFilter('All')}
            className={filter === 'All' ? 'bg-[#18546c] hover:bg-[#1a6985]' : ''}
          >
            All
          </Button>
          <Button
            variant={filter === 'pending' ? 'default' : 'outline'}
            onClick={() => setFilter('pending')}
            className={filter === 'pending' ? 'bg-[#18546c] hover:bg-[#1a6985]' : ''}
          >
            Pending
          </Button>
          <Button
            variant={filter === 'accepted' ? 'default' : 'outline'}
            onClick={() => setFilter('accepted')}
            className={filter === 'accepted' ? 'bg-[#18546c] hover:bg-[#1a6985]' : ''}
          >
            Accepted
          </Button>
          <Button
            variant={filter === 'expired' ? 'default' : 'outline'}
            onClick={() => setFilter('expired')}
            className={filter === 'expired' ? 'bg-[#18546c] hover:bg-[#1a6985]' : ''}
          >
            Expired
          </Button>
        </div>
      </div>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4" role="alert">
          <p className="font-bold">Error</p>
          <p>{error}</p>
        </div>
      )}
      
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Email</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Sent Date</TableHead>
                <TableHead>Expiry Date</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading && filteredInvitations.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    <div className="flex items-center justify-center">
                      <span className="animate-spin mr-2 text-xl">⟳</span>
                      Loading invitations...
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredInvitations.length > 0 ? (
                filteredInvitations.map((invitation) => (
                  <TableRow key={invitation.id}>
                    <TableCell>{invitation.email}</TableCell>
                    <TableCell>{invitation.role}</TableCell>
                    <TableCell>
                      <Badge className={getStatusBadgeColor(invitation.status)}>
                        {invitation.status.charAt(0).toUpperCase() + invitation.status.slice(1)}
                      </Badge>
                    </TableCell>
                    <TableCell>{formatDate(invitation.created_at)}</TableCell>
                    <TableCell>{formatDate(invitation.expires_at)}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          onClick={() => {}}
                          variant="ghost"
                          size="sm"
                        >
                          View
                        </Button>
                        <Button
                          onClick={() => {}}
                          variant="ghost"
                          size="sm"
                          disabled={invitation.status !== 'pending'}
                        >
                          Resend
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    No invitations found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      
      {totalPages > 1 && (
        <div className="flex justify-center mt-4">
          <div className="flex gap-2">
            <Button 
              onClick={() => setPage(p => Math.max(1, p - 1))}
              variant="outline"
              disabled={page === 1 || loading}
            >
              Previous
            </Button>
            <div className="flex items-center px-4 bg-gray-100 rounded">
              Page {page} of {totalPages}
            </div>
            <Button 
              onClick={() => setPage(p => Math.min(totalPages, p + 1))}
              variant="outline"
              disabled={page === totalPages || loading}
            >
              Next
            </Button>
          </div>
        </div>
      )}
      
      {/* Invite Suppliers Modal */}
      <InviteSuppliersModal 
        isOpen={isInviteModalOpen} 
        onClose={() => {
          setIsInviteModalOpen(false);
          // Refresh the invitations list after closing the modal
          handleRefresh();
        }} 
      />
    </div>
  );
}

'use client';

import { useState } from 'react';
import { SupplierDatabaseProvider } from '@/context/SupplierDatabaseContext';
import SupplierDatabaseSidebar from '@/components/Layouts/SupplierDatabaseSidebar';
import SupplierDatabaseHeader from '@/components/Layouts/SupplierDatabaseHeader';
import { SupplierDatabaseBreadcrumb } from './components/SupplierDatabaseBreadcrumb';
import { FiMenu } from 'react-icons/fi';

export default function SupplierDatabaseLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isSidebarOpen, setSidebarOpen] = useState(true);
  const [isMobileSidebarOpen, setMobileSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setSidebarOpen(!isSidebarOpen);
  };

  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!isMobileSidebarOpen);
  };

  return (
    <SupplierDatabaseProvider>
      <div className="flex h-full">
        {/* Mobile sidebar overlay */}
        {isMobileSidebarOpen && (
          <div 
            className="fixed inset-0 bg-black/50 z-40 md:hidden" 
            onClick={() => setMobileSidebarOpen(false)}
            aria-hidden="true"
          />
        )}
        
        {/* Desktop Sidebar */}
        <div className={`hidden md:block ${isSidebarOpen ? 'w-64' : 'w-0'} transition-all duration-300 ease-in-out`}>
          <SupplierDatabaseSidebar />
        </div>
        
        {/* Mobile Sidebar */}
        <div className="md:hidden">
          <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-slate-800 shadow-lg transform transition-transform duration-300 ease-in-out ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>
            <SupplierDatabaseSidebar onClose={() => setMobileSidebarOpen(false)} />
          </div>
        </div>
        
        <div className="flex-1 flex flex-col min-h-screen">
          <SupplierDatabaseHeader 
            toggleSidebar={toggleSidebar} 
            isSidebarOpen={isSidebarOpen}
            toggleMobileSidebar={toggleMobileSidebar}
          />
          <main className="flex-1 p-6 bg-gray-50 dark:bg-slate-900">
            <SupplierDatabaseBreadcrumb />
            {children}
          </main>
        </div>
      </div>
    </SupplierDatabaseProvider>
  );
}

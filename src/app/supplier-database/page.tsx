'use client';

import React from 'react';
import { useAuth } from '@/context/AuthContext';
import { useSupplierDatabase } from '@/context/SupplierDatabaseContext';
import SupplierTable from '@/components/supplier/SupplierTable';

export default function SupplierDatabasePage() {
  const { user } = useAuth();
  const { activeTab } = useSupplierDatabase();
  
  // If user is not available yet, show loading state
  if (!user) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading user data...</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="space-y-6 p-6">
      {/* Table component */}
      <SupplierTable filterStatus={activeTab as any} />
    </div>
  );
}

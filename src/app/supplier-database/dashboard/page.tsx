'use client';

import React, { useState } from 'react';
import { AppLayout } from '@/components/Layouts/AppLayout';
import SupplierDatabaseHeader from '@/components/Layouts/SupplierDatabaseHeader';
import AppSidebar from '@/components/Layouts/AppSidebar';

export default function SupplierDatabaseDashboardPage() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);
  const toggleMobileSidebar = () => setIsMobileSidebarOpen(!isMobileSidebarOpen);

  return (
    <AppLayout>
      <SupplierDatabaseHeader 
        toggleSidebar={toggleSidebar} 
        isSidebarOpen={isSidebarOpen} 
        toggleMobileSidebar={toggleMobileSidebar} 
      />
      <div className="flex flex-1 overflow-hidden">
        <AppSidebar />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="mb-6">
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Supplier Database Dashboard</h1>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <p className="text-gray-600 dark:text-gray-300">Supplier database dashboard content will go here.</p>
          </div>
        </main>
      </div>
    </AppLayout>
  );
}

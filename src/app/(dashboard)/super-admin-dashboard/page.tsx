'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import SuperAdminDashboard from '@/components/dashboard/SuperAdminDashboard';
import { Loading } from '@/components/global/Loading';

export default function SuperAdminDashboardPage() {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = React.useState(false);

  React.useEffect(() => {
    if (isAuthenticated === false) {
      router.push('/login');
    } else if (isAuthenticated === true && user) {
      // Only allow access if user is a super admin
      if (!user.roles || !user.roles.includes('super_admin')) {
        // If user is not a super admin, redirect to login
        alert('You do not have permission to access this page.');
        router.push('/login');
        return;
      }
    }
  }, [isAuthenticated, router, user]);

  if (!isAuthenticated || !user || isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Super Admin Dashboard</h1>
        <Loading fullScreen={false} text="Loading dashboard data..." size="lg" className="py-12" />
      </div>
    );
  }

  return <SuperAdminDashboard user={user} />;
}

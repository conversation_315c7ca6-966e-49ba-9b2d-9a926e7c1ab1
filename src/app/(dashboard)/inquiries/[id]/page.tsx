'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/InquiryCard';
import { ArrowLeftIcon, ArrowDownTrayIcon, DocumentTextIcon, ChatBubbleLeftIcon, PrinterIcon, ShareIcon } from '@heroicons/react/24/solid';
import Link from 'next/link';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

// Helper function for status badge styling
const getStatusBadge = (status: string) => {
  const baseClasses = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium';
  
  switch (status) {
    case 'Pending':
      return `${baseClasses} bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400`;
    case 'In Progress':
      return `${baseClasses} bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400`;
    case 'Completed':
      return `${baseClasses} bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400`;
    default:
      return `${baseClasses} bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300`;
  }
};

// Mock data - replace with actual API call
const getInquiryDetails = async (id: string) => {
  console.log('getInquiryDetails called with id:', id);
  // Simulate API call with a small delay
  await new Promise(resolve => setTimeout(resolve, 300));
  
  const mockInquiries = [
    {
      id: '1',
      client: 'Acme Corporation',
      year: 2023,
      caseNo: 'C-2023-045',
      reference: 'INQ-045-23',
      subject: 'Annual Financial Audit',
      status: 'Pending',
      dateRequested: '2023-06-15',
      deliveryRequired: '2023-07-15',
      description: 'Comprehensive audit of financial statements for the fiscal year 2023, including balance sheet, income statement, and cash flow statement.',
      priority: 'High',
      assignedTo: 'John Doe',
      lastUpdated: '2023-06-20T14:30:00Z',
      attachments: [
        { name: 'Financial_Report_2023.pdf', type: 'PDF', size: '2.4 MB' },
        { name: 'Supporting_Docs.zip', type: 'ZIP', size: '5.7 MB' }
      ]
    },
    {
      id: '2',
      client: 'Globex Industries',
      year: 2023,
      caseNo: 'C-2023-129',
      reference: 'INQ-129-23',
      subject: 'Q2 Financial Review',
      status: 'In Progress',
      dateRequested: '2023-06-18',
      deliveryRequired: '2023-07-05',
      description: 'Quarterly financial review and analysis for Q2 2023.',
      priority: 'Medium',
      assignedTo: 'Jane Smith',
      lastUpdated: '2023-06-25T10:15:00Z',
      attachments: [
        { name: 'Q2_Report_2023.pdf', type: 'PDF', size: '1.8 MB' }
      ]
    }
  ];

  const foundInquiry = mockInquiries.find(inquiry => inquiry.id === id);
  console.log('Found inquiry:', foundInquiry);
  
  return foundInquiry || mockInquiries[0];
};

interface InquiryContentProps {
  id: string;
  isAuthenticated?: boolean | null;
  onBack: () => void;
  user: any; // Replace 'any' with your User type if available
}

function InquiryContent({ id, isAuthenticated, onBack, user }: InquiryContentProps) {
  const router = useRouter();
  const [inquiry, setInquiry] = React.useState<any>(null);
  const [isLoading, setIsLoading] = React.useState(true);

  useEffect(() => {
    console.log('useEffect triggered', { isAuthenticated, id });
    
    if (isAuthenticated === false) {
      console.log('Not authenticated, redirecting to sign-in');
      router.push('/auth/sign-in');
      return;
    }

    const fetchInquiry = async () => {
      console.log('Starting to fetch inquiry with ID:', id);
      try {
        const data = await getInquiryDetails(id);
        console.log('Fetched inquiry data:', data);
        setInquiry(data);
      } catch (error) {
        console.error('Error fetching inquiry:', error);
      } finally {
        console.log('Finished loading, setting isLoading to false');
        setIsLoading(false);
      }
    };

    if (isAuthenticated && id) {
      console.log('User is authenticated and has ID, fetching inquiry');
      fetchInquiry();
    } else {
      console.log('Not fetching because:', { isAuthenticated, hasId: !!id });
    }
  }, [isAuthenticated, id, router]);

  // Rest of your component JSX
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="mb-4">Loading inquiry details...</div>
          <Button onClick={onBack}>Back to Inquiries</Button>
        </div>
      </div>
    );
  }

  if (!inquiry) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-red-500 mb-4">Error: Could not load inquiry</div>
          <Button onClick={onBack}>Back to Inquiries</Button>
        </div>
      </div>
    );
  }

  // Format date helper
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Header with back button */}
      <div className="mb-6">
        <Button 
          variant="ghost" 
          onClick={onBack}
          className="flex items-center text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
        >
          <ArrowLeftIcon className="mr-2 h-4 w-4" />
          Back to Inquiries
        </Button>
      </div>

      {/* Main content */}
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
        {/* Main content */}
        <div className="lg:col-span-8 space-y-6">
          {/* Details card */}
          <Card className="border border-gray-200 dark:border-gray-800">
            <CardHeader className="pb-4">
              <div className="flex justify-between items-start">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{inquiry.subject}</h1>
                  <div className="flex items-center mt-2 space-x-2">
                    <span className={getStatusBadge(inquiry.status)}>{inquiry.status}</span>
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      Case #{inquiry.caseNo}
                    </span>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" className="text-gray-600 dark:text-gray-300">
                    <PrinterIcon className="w-4 h-4 mr-2" />
                    Print
                  </Button>
                  <Button variant="outline" size="sm" className="text-gray-600 dark:text-gray-300">
                    <ShareIcon className="w-4 h-4 mr-2" />
                    Share
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="prose dark:prose-invert max-w-none">
                <p className="text-gray-700 dark:text-gray-300">{inquiry.description}</p>
              </div>
            </CardContent>
          </Card>

          {/* Attachments */}
          {inquiry.attachments && inquiry.attachments.length > 0 && (
            <Card className="border border-gray-200 dark:border-gray-800">
              <CardHeader>
                <CardTitle className="text-lg font-semibold">Attachments</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {inquiry.attachments.map((file: any, index: number) => (
                    <div key={index} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                      <div className="flex items-center">
                        <div className="p-2 bg-gray-100 dark:bg-gray-700 rounded-md mr-3">
                          <DocumentTextIcon className="h-5 w-5 text-gray-600 dark:text-gray-300" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">{file.name}</p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">{file.size} • {file.type}</p>
                        </div>
                      </div>
                      <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
                        <ArrowDownTrayIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Activity */}
          <Card className="border border-gray-200 dark:border-gray-800">
            <CardHeader>
              <CardTitle className="text-lg font-semibold">Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="flex items-start">
                  <div className="flex-shrink-0 mr-3">
                    <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                      <ChatBubbleLeftIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">New inquiry created</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(inquiry.dateRequested).toLocaleDateString()}
                      </p>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                      Inquiry was created and is now pending review.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="lg:col-span-4 space-y-6">
          {/* Details */}
          <Card className="border border-gray-200 dark:border-gray-800">
            <CardHeader>
              <CardTitle className="text-lg font-semibold">Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Client</p>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">{inquiry.client}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Reference</p>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">{inquiry.reference}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Date Requested</p>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">
                    {formatDate(inquiry.dateRequested)}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Delivery Required By</p>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">
                    {formatDate(inquiry.deliveryRequired)}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Priority</p>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">{inquiry.priority}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Assigned To</p>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">{inquiry.assignedTo}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card className="border border-gray-200 dark:border-gray-800">
            <CardHeader>
              <CardTitle className="text-lg font-semibold">Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full justify-start" variant="outline">
                <ChatBubbleLeftIcon className="w-4 h-4 mr-2" />
                Send Message
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <PrinterIcon className="w-4 h-4 mr-2" />
                Generate Report
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <ShareIcon className="w-4 h-4 mr-2" />
                Share Inquiry
              </Button>
              {inquiry.status === 'Pending' && (
                <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                  Start Working
                </Button>
              )}
              {inquiry.status === 'In Progress' && (
                <Button className="w-full bg-green-600 hover:bg-green-700 text-white">
                  Mark as Completed
                </Button>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

export default function InquiryDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const id = Array.isArray(params.id) ? params.id[0] : params.id;
  const { user, isAuthenticated } = useAuth();

  const handleBack = () => {
    router.push('/inquiries');
  };

  if (!id) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-red-500 mb-4">Error: Inquiry ID is missing</div>
          <Button onClick={handleBack}>Back to Inquiries</Button>
        </div>
      </div>
    );
  }

  return <InquiryContent id={id} isAuthenticated={isAuthenticated} onBack={handleBack} user={user} />;
}

'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, FiBell, FiCreditCard, FiGlobe, FiMoon, FiSun, FiMail } from 'react-icons/fi';
import Link from 'next/link';

const SettingsPage = () => {
  const [darkMode, setDarkMode] = useState(false);
  const [notifications, setNotifications] = useState({
    email: true,
    push: true,
    monthlyReport: false,
  });

  const toggleNotification = (key: keyof typeof notifications) => {
    setNotifications(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const settingsSections = [
    {
      title: 'Account',
      icon: <FiUser className="w-5 h-5" />,
      items: [
        { label: 'Profile Information', description: 'Update your name, email, and profile picture' },
        { label: 'Password', description: 'Change your password' },
        { label: 'Two-Factor Authentication', description: 'Add an extra layer of security' },
      ]
    },
    {
      title: 'Communications',
      icon: <FiMail className="w-5 h-5" />,
      items: [
        { 
          label: 'Email Templates', 
          description: 'Manage and customize email templates',
          link: '/settings/email-templates'
        },
      ]
    },
    {
      title: 'Notifications',
      icon: <FiBell className="w-5 h-5" />,
      items: [
        { 
          label: 'Email Notifications', 
          description: 'Receive email notifications',
          toggle: notifications.email,
          toggleAction: () => toggleNotification('email')
        },
        { 
          label: 'Push Notifications', 
          description: 'Receive push notifications',
          toggle: notifications.push,
          toggleAction: () => toggleNotification('push')
        },
        { 
          label: 'Monthly Reports', 
          description: 'Receive monthly summary reports',
          toggle: notifications.monthlyReport,
          toggleAction: () => toggleNotification('monthlyReport')
        },
      ]
    },
    {
      title: 'Billing',
      icon: <FiCreditCard className="w-5 h-5" />,
      items: [
        { label: 'Payment Methods', description: 'Manage your payment methods' },
        { label: 'Billing History', description: 'View your billing history' },
        { label: 'Invoices', description: 'Download your invoices' },
      ]
    },
    {
      title: 'Preferences',
      icon: <FiGlobe className="w-5 h-5" />,
      items: [
        { 
          label: 'Dark Mode', 
          description: 'Toggle between light and dark theme',
          toggle: darkMode,
          toggleAction: () => setDarkMode(!darkMode),
          customToggle: true
        },
        { label: 'Language', description: 'English (United States)' },
        { label: 'Time Zone', description: 'GMT+3 (EAT)' },
      ]
    },
  ];

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Settings</h1>
        <p className="text-gray-500 dark:text-gray-400 mt-1">Manage your account settings and preferences</p>
      </div>

      <div className="space-y-6">
        {settingsSections.map((section, sectionIndex) => (
          <div key={sectionIndex} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center">
              <span className="text-primary-600 dark:text-primary-400 mr-3">
                {section.icon}
              </span>
              <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                {section.title}
              </h2>
            </div>
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {section.items.map((item, itemIndex) => (
                <div key={itemIndex} className="px-6 py-4 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                  <div className="flex-1">
                    <h3 className="text-sm font-medium text-gray-900 dark:text-white">{item.label}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{item.description}</p>
                  </div>
                  {'link' in item ? (
                    <Link href={item.link} className="text-[#18546c] hover:text-[#1a6985] flex items-center">
                      <span className="text-sm font-medium">View</span>
                      <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  ) : item.toggle !== undefined ? (
                    'customToggle' in item && item.customToggle ? (
                      <button
                        onClick={item.toggleAction}
                        type="button"
                        className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${
                          item.toggle ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-600'
                        }`}
                        role="switch"
                        aria-checked={item.toggle}
                      >
                        <span className="sr-only">Toggle {item.label}</span>
                        <span
                          aria-hidden="true"
                          className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                            item.toggle ? 'translate-x-5' : 'translate-x-0'
                          }`}
                        >
                          {item.toggle ? (
                            <FiMoon className="h-3 w-3 text-gray-600 m-1" />
                          ) : (
                            <FiSun className="h-3 w-3 text-yellow-500 m-1" />
                          )}
                        </span>
                      </button>
                    ) : (
                      <button
                        onClick={item.toggleAction}
                        type="button"
                        className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${
                          item.toggle ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-600'
                        }`}
                        role="switch"
                        aria-checked={item.toggle}
                      >
                        <span className="sr-only">Toggle {item.label}</span>
                        <span
                          aria-hidden="true"
                          className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                            item.toggle ? 'translate-x-5' : 'translate-x-0'
                          }`}
                        />
                      </button>
                    )
                  ) : (
                    <button
                      type="button"
                      className="inline-flex items-center text-sm font-medium text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300"
                    >
                      Change
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Danger Zone</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Permanently delete your account and all of your data.
            </p>
          </div>
          <button
            type="button"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Delete Account
          </button>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;

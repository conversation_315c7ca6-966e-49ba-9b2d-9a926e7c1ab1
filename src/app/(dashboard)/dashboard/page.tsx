'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { Loading } from '@/components/global/Loading';

export default function DashboardPage() {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (isAuthenticated === false) {
      console.log('Dashboard: Not authenticated, redirecting to login');
      router.push('/login');
      return;
    }

    if (isAuthenticated === true && user) {
      console.log('Dashboard: User is authenticated, redirecting based on role');
      // Redirect based on user role
      if (user.roles?.includes('super_admin')) {
        router.push('/super-admin-dashboard');
      } else if (user.roles?.includes('s_admin')) {
        router.push('/supplier-dashboard');
      } else if (user.roles?.includes('c_admin')) {
        router.push('/company-dashboard');
      } else {
        // Default fallback if role is not recognized
        console.warn('Unknown user role, defaulting to company dashboard');
        router.push('/company-dashboard');
      }
    }
  }, [isAuthenticated, router, user]);

  // Show loading state while redirecting
  return <Loading fullScreen text="Loading your dashboard..." size="lg" />;
}

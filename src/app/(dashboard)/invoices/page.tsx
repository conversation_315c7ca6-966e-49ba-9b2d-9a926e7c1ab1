'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ilt<PERSON>, FiChevronDown, FiChevronUp, FiFileText } from 'react-icons/fi';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';

// Mock data for the table
const mockInvoices = [
  {
    id: 'INV-2023-001',
    year: '2023',
    caseNo: 'C-001',
    reference: 'REF-001',
    subject: 'Office Supplies Q1',
    status: 'Paid',
    dateCreated: '2023-01-15',
    budgetCode: 'BGT-2023-OFF',
    budgetCategory: 'Office Expenses',
  },
  {
    id: 'INV-2023-002',
    year: '2023',
    caseNo: 'C-002',
    reference: 'REF-002',
    subject: 'IT Equipment Purchase',
    status: 'Paid',
    dateCreated: '2023-02-20',
    budgetCode: 'BGT-2023-IT',
    budgetCategory: 'IT Infrastructure',
  },
  {
    id: 'INV-2023-003',
    year: '2023',
    caseNo: 'C-003',
    reference: 'REF-003',
    subject: 'Software Licenses',
    status: 'Paid',
    dateCreated: '2023-03-10',
    budgetCode: 'BGT-2023-SW',
    budgetCategory: 'Software',
  },
  {
    id: 'INV-2023-004',
    year: '2023',
    caseNo: 'C-004',
    reference: 'REF-004',
    subject: 'Office Furniture',
    status: 'Paid',
    dateCreated: '2023-04-05',
    budgetCode: 'BGT-2023-OFF',
    budgetCategory: 'Office Expenses',
  },
  {
    id: 'INV-2023-005',
    year: '2023',
    caseNo: 'C-005',
    reference: 'REF-005',
    subject: 'Marketing Materials',
    status: 'Paid',
    dateCreated: '2023-05-12',
    budgetCode: 'BGT-2023-MKT',
    budgetCategory: 'Marketing',
  },
  {
    id: 'INV-2023-006',
    year: '2023',
    caseNo: 'C-006',
    reference: 'REF-006',
    subject: 'Cloud Services',
    status: 'Paid',
    dateCreated: '2023-06-18',
    budgetCode: 'BGT-2023-IT',
    budgetCategory: 'IT Infrastructure',
  },
  {
    id: 'INV-2023-007',
    year: '2023',
    caseNo: 'C-007',
    reference: 'REF-007',
    subject: 'Team Building Event',
    status: 'Paid',
    dateCreated: '2023-07-22',
    budgetCode: 'BGT-2023-HR',
    budgetCategory: 'Human Resources',
  },
  {
    id: 'INV-2023-008',
    year: '2023',
    caseNo: 'C-008',
    reference: 'REF-008',
    subject: 'Office Renovation',
    status: 'Paid',
    dateCreated: '2023-08-30',
    budgetCode: 'BGT-2023-OFF',
    budgetCategory: 'Office Expenses',
  },
  {
    id: 'INV-2023-009',
    year: '2023',
    caseNo: 'C-009',
    reference: 'REF-009',
    subject: 'Training Workshop',
    status: 'Paid',
    dateCreated: '2023-09-14',
    budgetCode: 'BGT-2023-TRN',
    budgetCategory: 'Training',
  },
  {
    id: 'INV-2023-010',
    year: '2023',
    caseNo: 'C-010',
    reference: 'REF-010',
    subject: 'Security System Upgrade',
    status: 'Paid',
    dateCreated: '2023-10-05',
    budgetCode: 'BGT-2023-IT',
    budgetCategory: 'IT Infrastructure',
  },
  {
    id: 'INV-2023-011',
    year: '2023',
    caseNo: 'C-011',
    reference: 'REF-011',
    subject: 'Year-end Software Renewals',
    status: 'Paid',
    dateCreated: '2023-11-20',
    budgetCode: 'BGT-2023-SW',
    budgetCategory: 'Software',
  },
];

const statusVariantMap = {
  'Paid': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
  'Pending': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
  'Overdue': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
  'Draft': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
};

export default function InvoicesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' } | null>(null);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const handleSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const sortedInvoices = [...mockInvoices].sort((a, b) => {
    if (!sortConfig) return 0;
    
    const aValue = a[sortConfig.key as keyof typeof a];
    const bValue = b[sortConfig.key as keyof typeof b];
    
    if (aValue < bValue) {
      return sortConfig.direction === 'asc' ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortConfig.direction === 'asc' ? 1 : -1;
    }
    return 0;
  });

  const filteredInvoices = sortedInvoices.filter(invoice => {
    const searchLower = searchTerm.toLowerCase();
    return (
      invoice.id.toLowerCase().includes(searchLower) ||
      invoice.year.includes(searchLower) ||
      invoice.subject.toLowerCase().includes(searchLower) ||
      invoice.reference.toLowerCase().includes(searchLower)
    );
  });

  return (
    <div className="p-6 bg-gray-50 dark:bg-slate-900 min-h-screen">
      {/* Breadcrumb */}
      <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-4">
        <Link href="/dashboard" className="hover:text-gray-700 dark:hover:text-gray-300">
          Dashboard
        </Link>
        <span className="mx-2">/</span>
        <span className="text-gray-700 dark:text-gray-300">Invoices</span>
      </div>

      {/* Page Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Invoices</h1>
          <p className="text-gray-600 dark:text-gray-400">All relevant projects</p>
        </div>
        <Button className="bg-[#18546c] hover:bg-[#0d3d50] text-white">
          + Create New
        </Button>
      </div>

      {/* Action Bar */}
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 p-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              placeholder="Search ID, Year, Subject, Reference..."
              className="pl-10 w-full"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <FiFilter className="h-4 w-4" />
                Filter
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56">
              <DropdownMenuItem>Status: All</DropdownMenuItem>
              <DropdownMenuItem>Status: Paid</DropdownMenuItem>
              <DropdownMenuItem>Status: Pending</DropdownMenuItem>
              <DropdownMenuItem>Status: Overdue</DropdownMenuItem>
              <DropdownMenuItem>Status: Draft</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <span>Sort by</span>
                {sortConfig?.direction === 'asc' ? (
                  <FiChevronUp className="h-4 w-4" />
                ) : (
                  <FiChevronDown className="h-4 w-4" />
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => handleSort('dateCreated')}>
                Date Created
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleSort('subject')}>
                Subject
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleSort('status')}>
                Status
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Data Table */}
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50 dark:bg-slate-700">
                <TableHead className="font-medium text-gray-700 dark:text-gray-300">
                  <button 
                    className="flex items-center gap-1"
                    onClick={() => handleSort('id')}
                  >
                    Project ID
                    {sortConfig?.key === 'id' && (
                      sortConfig.direction === 'asc' ? 
                      <FiChevronUp className="h-4 w-4" /> : 
                      <FiChevronDown className="h-4 w-4" />
                    )}
                  </button>
                </TableHead>
                <TableHead className="font-medium text-gray-700 dark:text-gray-300">
                  <button 
                    className="flex items-center gap-1"
                    onClick={() => handleSort('year')}
                  >
                    Year
                    {sortConfig?.key === 'year' && (
                      sortConfig.direction === 'asc' ? 
                      <FiChevronUp className="h-4 w-4" /> : 
                      <FiChevronDown className="h-4 w-4" />
                    )}
                  </button>
                </TableHead>
                <TableHead className="font-medium text-gray-700 dark:text-gray-300">Case No.</TableHead>
                <TableHead className="font-medium text-gray-700 dark:text-gray-300">Reference</TableHead>
                <TableHead className="font-medium text-gray-700 dark:text-gray-300">
                  <button 
                    className="flex items-center gap-1"
                    onClick={() => handleSort('subject')}
                  >
                    Subject
                    {sortConfig?.key === 'subject' && (
                      sortConfig.direction === 'asc' ? 
                      <FiChevronUp className="h-4 w-4" /> : 
                      <FiChevronDown className="h-4 w-4" />
                    )}
                  </button>
                </TableHead>
                <TableHead className="font-medium text-gray-700 dark:text-gray-300">
                  <button 
                    className="flex items-center gap-1"
                    onClick={() => handleSort('status')}
                  >
                    Status
                    {sortConfig?.key === 'status' && (
                      sortConfig.direction === 'asc' ? 
                      <FiChevronUp className="h-4 w-4" /> : 
                      <FiChevronDown className="h-4 w-4" />
                    )}
                  </button>
                </TableHead>
                <TableHead className="font-medium text-gray-700 dark:text-gray-300">Date created</TableHead>
                <TableHead className="font-medium text-gray-700 dark:text-gray-300">Budget code</TableHead>
                <TableHead className="font-medium text-gray-700 dark:text-gray-300">Budget category</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredInvoices.length > 0 ? (
                filteredInvoices.map((invoice) => (
                  <TableRow key={invoice.id} className="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                    <TableCell className="font-medium">
                      <Link 
                        href={`/invoices/${invoice.id}`}
                        className="text-blue-600 hover:underline dark:text-blue-400"
                      >
                        {invoice.id}
                      </Link>
                    </TableCell>
                    <TableCell>{invoice.year}</TableCell>
                    <TableCell>{invoice.caseNo}</TableCell>
                    <TableCell>{invoice.reference}</TableCell>
                    <TableCell className="max-w-xs truncate">{invoice.subject}</TableCell>
                    <TableCell>
                      <Badge 
                        className={`text-xs font-medium px-2 py-1 rounded-full ${
                          statusVariantMap[invoice.status as keyof typeof statusVariantMap] || 
                          'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                        }`}
                      >
                        {invoice.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{new Date(invoice.dateCreated).toLocaleDateString()}</TableCell>
                    <TableCell>{invoice.budgetCode}</TableCell>
                    <TableCell>{invoice.budgetCategory}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8 text-gray-500 dark:text-gray-400">
                    <div className="flex flex-col items-center justify-center space-y-2">
                      <FiFileText className="h-12 w-12 text-gray-300 dark:text-gray-600" />
                      <p>No invoices found</p>
                      <p className="text-sm">Try adjusting your search or filter criteria</p>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        <div className="px-6 py-4 border-t border-gray-200 dark:border-slate-700 flex flex-col sm:flex-row items-center justify-between gap-4">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Showing <span className="font-medium">1</span> to{' '}
            <span className="font-medium">{Math.min(rowsPerPage, filteredInvoices.length)}</span> of{' '}
            <span className="font-medium">{filteredInvoices.length}</span> entries
          </div>
          
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500 dark:text-gray-400">Rows per page:</span>
            <select
              className="bg-white dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-md px-2 py-1 text-sm"
              value={rowsPerPage}
              onChange={(e) => setRowsPerPage(Number(e.target.value))}
            >
              {[10, 25, 50, 100].map((size) => (
                <option key={size} value={size}>
                  {size}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-center space-x-2">
            <button
              className="px-3 py-1 border border-gray-200 dark:border-slate-600 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={true}
            >
              Previous
            </button>
            <button className="px-3 py-1 bg-blue-600 text-white rounded-md text-sm font-medium">
              1
            </button>
            <button
              className="px-3 py-1 border border-gray-200 dark:border-slate-600 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={true}
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

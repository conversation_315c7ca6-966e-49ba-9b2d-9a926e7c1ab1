'use client';

import { useState } from 'react';
import AppSidebar, { MobileMenuButton } from '@/components/Layouts/AppSidebar';
import AppHeader from '@/components/Layouts/AppHeader';
import EmailVerificationBanner from '@/components/EmailVerificationBanner';
import { Providers } from '../providers';
import '../../css/style.css';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  const toggleMobileSidebar = () => {
    setIsMobileSidebarOpen(!isMobileSidebarOpen);
  };

  const closeMobileSidebar = () => {
    setIsMobileSidebarOpen(false);
  };

  const toggleSidebarCollapse = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  return (
    <Providers>
      <div className="flex h-screen bg-gray-50 dark:bg-slate-900">
        {/* Mobile sidebar overlay */}
        {isMobileSidebarOpen && (
          <div 
            className="fixed inset-0 bg-black/50 z-40 md:hidden" 
            onClick={closeMobileSidebar}
            aria-hidden="true"
          />
        )}
        
        {/* Sidebar - single instance for both mobile and desktop */}
        <AppSidebar
          isMobileOpen={isMobileSidebarOpen}
          onMobileClose={closeMobileSidebar}
          isCollapsed={isSidebarCollapsed}
          onToggleCollapse={toggleSidebarCollapse}
        />
        
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex items-center h-16 bg-[#2A6E78]">
            <MobileMenuButton onClick={toggleMobileSidebar} />
            <AppHeader />
          </div>
          <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 dark:bg-slate-900 p-6">
            <EmailVerificationBanner />
            {children}
          </main>
        </div>
      </div>
    </Providers>
  );
}

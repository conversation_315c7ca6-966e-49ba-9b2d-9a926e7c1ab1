'use client';

import React from 'react';
import Link from 'next/link';
import { FiPlus, FiSearch, FiFilter, FiDownload, FiChevronRight } from 'react-icons/fi';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';

export default function InventoryPage() {
  // First row cards data
  const firstRowCards = [
    { 
      title: 'Assets', 
      value: '37', 
      icon: '🔧',
      iconBg: 'bg-blue-100 dark:bg-blue-900/30',
      iconColor: 'text-blue-600 dark:text-blue-400',
      hoverColor: 'blue',
      link: '/inventory/assets',
      className: 'w-full max-w-xs'
    },
    { 
      title: 'Stores/Warehouses', 
      value: '4', 
      icon: '🏠',
      iconBg: 'bg-green-100 dark:bg-green-900/30',
      iconColor: 'text-green-600 dark:text-green-400',
      hoverColor: 'green',
      link: '/inventory/stores',
      className: 'w-full max-w-xs'
    },
  ];

  // Second row cards data
  const secondRowCards = [
    { 
      title: 'Draft Requisitions', 
      value: '0', 
      icon: '⏱️',
      iconBg: 'bg-amber-100 dark:bg-amber-900/30',
      iconColor: 'text-amber-600 dark:text-amber-400',
      hoverColor: 'orange',
      link: '/inventory/requisitions?status=draft'
    },
    { 
      title: 'Submitted Requisitions', 
      value: '3', 
      icon: '📋',
      iconBg: 'bg-purple-100 dark:bg-purple-900/30',
      iconColor: 'text-purple-600 dark:text-purple-400',
      hoverColor: 'pink',
      link: '/inventory/requisitions?status=submitted'
    },
    { 
      title: 'Approved', 
      value: '6', 
      icon: '🛡️',
      iconBg: 'bg-emerald-100 dark:bg-emerald-900/30',
      iconColor: 'text-emerald-600 dark:text-emerald-400',
      hoverColor: 'green',
      link: '/inventory/requisitions?status=approved'
    },
    { 
      title: 'Closed Requisitions', 
      value: '14', 
      icon: '✅',
      iconBg: 'bg-green-100 dark:bg-green-900/30',
      iconColor: 'text-green-600 dark:text-green-400',
      hoverColor: 'green',
      link: '/inventory/requisitions?status=closed'
    },
  ];

  // Hover styles mapping
  const hoverStyles = {
    blue: {
      border: 'group-hover:border-blue-300',
      sectionBg: 'group-hover:bg-blue-50 dark:group-hover:bg-blue-900/20',
      text: 'group-hover:text-blue-600 dark:group-hover:text-blue-400',
    },
    green: {
      border: 'group-hover:border-green-300',
      sectionBg: 'group-hover:bg-green-50 dark:group-hover:bg-green-900/20',
      text: 'group-hover:text-green-600 dark:group-hover:text-green-400',
    },
    orange: {
      border: 'group-hover:border-orange-300',
      sectionBg: 'group-hover:bg-orange-50 dark:group-hover:bg-orange-900/20',
      text: 'group-hover:text-orange-600 dark:group-hover:text-orange-400',
    },
    pink: {
      border: 'group-hover:border-pink-300',
      sectionBg: 'group-hover:bg-pink-50 dark:group-hover:bg-pink-900/20',
      text: 'group-hover:text-pink-600 dark:group-hover:text-pink-400',
    },
  };

  // Render card component
  const renderCard = (card: any) => {
    const styles = hoverStyles[card.hoverColor as keyof typeof hoverStyles] || hoverStyles.blue;
    
    return (
      <div 
        key={card.title}
        className="group bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-transparent hover:border-gray-200 dark:hover:border-slate-700 p-6 transition-all duration-200 cursor-pointer"
      >
        <div className="flex items-start space-x-4 mb-6">
          <div className={`p-2.5 rounded-lg ${card.iconBg} ${card.iconColor} text-xl`}>
            {card.icon}
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">{card.title}</p>
            <p className="text-3xl font-bold text-gray-900 dark:text-gray-100 mt-2">{card.value}</p>
          </div>
        </div>
        <div className={`mt-4 pt-4 -mx-6 -mb-6 px-6 pb-4 rounded-b-lg transition-colors ${styles.sectionBg} ${styles.border}`}>
          <div className="ml-[75px]">
            <Link 
              href={card.link} 
              className={`text-sm font-medium flex items-center transition-colors text-gray-500 dark:text-gray-400 ${styles.text} hover:opacity-80`}
            >
              View Details
              <FiChevronRight className="ml-1 h-4 w-4" />
            </Link>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 sm:space-y-0 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Assets & Inventory</h1>
          <p className="text-gray-500 dark:text-gray-400 text-sm">
            Manage your organization&apos;s assets and inventory
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Link href="/inventory/new">
            <Button className="bg-blue-600 hover:bg-blue-700 text-white">
              <FiPlus className="mr-2 h-4 w-4" />
              Add Asset
            </Button>
          </Link>
        </div>
      </div>

      {/* First Row - Centered */}
      <div className="flex flex-wrap justify-center gap-6">
        {firstRowCards.map(card => (
          <div key={card.title} className={card.className}>
            {renderCard(card)}
          </div>
        ))}
      </div>

      {/* Second Row - Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {secondRowCards.map(card => (
          <div key={card.title}>
            {renderCard(card)}
          </div>
        ))}
      </div>

      {/* Reports Section */}
      <div className="group bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-transparent hover:border-gray-200 dark:hover:border-slate-700 p-6 transition-all duration-200 cursor-pointer">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-4">
            <div className="p-3 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 rounded-lg text-2xl">
              📊
            </div>
            <div>
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">Assets & Inventory Reports</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                Generate and view detailed reports for your assets and inventory
              </p>
            </div>
          </div>
          <Link 
            href="/dashboard/reports/inventory"
            className="inline-flex items-center text-sm font-medium text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300 transition-colors group-hover:underline"
          >
            Continue
            <FiChevronRight className="ml-1 h-4 w-4" />
          </Link>
        </div>
      </div>
    </div>
  );
}

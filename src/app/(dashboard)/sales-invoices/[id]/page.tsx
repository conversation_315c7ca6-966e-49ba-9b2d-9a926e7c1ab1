'use client';

import React from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { FiArrowLeft, FiEdit, FiPrinter, FiShare2, FiDownload, FiFileText, FiCalendar, FiDollarSign, FiUser, FiMail, FiPhone, FiMapPin, FiCreditCard } from 'react-icons/fi';
import { Loading } from '@/components/global/Loading';

// Mock data for sales invoice details
const mockInvoice = {
  id: 'INV-2023-1001',
  customer: 'ABC Corporation',
  customerId: 'CUST-001',
  date: '2023-10-15',
  dueDate: '2023-11-14',
  status: 'Paid',
  currency: 'USD',
  subtotal: 11250.00,
  tax: 1250.00,
  total: 12500.00,
  paymentDate: '2023-10-20',
  paymentMethod: 'Bank Transfer',
  notes: 'Thank you for your business. Please include the invoice number in your payment reference.',
  terms: 'Payment due within 30 days of invoice date.',
  items: [
    {
      id: 1,
      name: 'Office Chair',
      description: 'Ergonomic office chair with lumbar support',
      quantity: 5,
      unitPrice: 1500.00,
      total: 7500.00
    },
    {
      id: 2,
      name: 'Standing Desk',
      description: 'Adjustable height standing desk',
      quantity: 2,
      unitPrice: 1875.00,
      total: 3750.00
    },
    {
      id: 3,
      name: 'Monitor Arm',
      description: 'Dual monitor arm mount',
      quantity: 5,
      unitPrice: 200.00,
      total: 1000.00
    }
  ],
  billingAddress: {
    name: 'ABC Corporation',
    company: 'ABC Corporation',
    street: '123 Business Ave',
    city: 'New York',
    state: 'NY',
    zip: '10001',
    country: 'United States',
    email: '<EMAIL>',
    phone: '+****************'
  },
  shippingAddress: {
    name: 'John Doe',
    company: 'ABC Corporation',
    street: '456 Shipping St',
    city: 'New York',
    state: 'NY',
    zip: '10002',
    country: 'United States',
    email: '<EMAIL>',
    phone: '+****************'
  },
  history: [
    {
      id: '1',
      date: '2023-10-15T10:30:00Z',
      status: 'Created',
      description: 'Invoice created',
      user: 'John Smith'
    },
    {
      id: '2',
      date: '2023-10-15T11:15:00Z',
      status: 'Sent',
      description: 'Invoice sent to customer',
      user: 'John Smith'
    },
    {
      id: '3',
      date: '2023-10-20T09:45:00Z',
      status: 'Paid',
      description: 'Payment received',
      user: 'System'
    }
  ]
};

const SalesInvoiceDetailPage = () => {
  const { id } = useParams();
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = React.useState(true);
  const [invoice, setInvoice] = React.useState<any>(null);
  const [activeTab, setActiveTab] = React.useState('details');

  React.useEffect(() => {
    if (isAuthenticated === false) {
      router.push('/login');
    } else if (isAuthenticated === true) {
      // Simulate API call to fetch invoice details
      const timer = setTimeout(() => {
        setInvoice(mockInvoice);
        setIsLoading(false);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [isAuthenticated, router, id]);

  const getStatusBadge = (status: string) => {
    const baseClasses = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium';
    switch (status) {
      case 'Paid':
        return `${baseClasses} bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400`;
      case 'Pending':
        return `${baseClasses} bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400`;
      case 'Overdue':
        return `${baseClasses} bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400`;
      case 'Partially Paid':
        return `${baseClasses} bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300`;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatDateTime = (dateTimeString: string) => {
    return new Date(dateTimeString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: invoice?.currency || 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  if (!isAuthenticated || isLoading || !invoice) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Loading fullScreen={false} text="Loading invoice details..." size="lg" className="py-12" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Header with back button and actions */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div className="flex items-center mb-4 sm:mb-0">
          <Button
            variant="ghost"
            size="sm"
            className="mr-4"
            onClick={() => router.back()}
          >
            <FiArrowLeft className="h-4 w-4 mr-2" />
            Back to Invoices
          </Button>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Invoice: {invoice.id}
          </h1>
        </div>
        <div className="flex space-x-2 w-full sm:w-auto">
          <Button variant="outline" size="sm" className="flex items-center">
            <FiPrinter className="h-4 w-4 mr-2" />
            Print
          </Button>
          <Button variant="outline" size="sm" className="flex items-center">
            <FiShare2 className="h-4 w-4 mr-2" />
            Share
          </Button>
          <Button variant="outline" size="sm" className="flex items-center">
            <FiDownload className="h-4 w-4 mr-2" />
            Download
          </Button>
          <Button className="bg-[#18546c] hover:bg-[#134048] dark:bg-[#18546c] dark:hover:bg-[#134048] flex items-center">
            <FiEdit className="h-4 w-4 mr-2" />
            Edit
          </Button>
        </div>
      </div>

      {/* Status and action bar */}
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 p-4 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
          <div className="flex items-center mb-4 sm:mb-0">
            <div className="mr-4">
              <span className={getStatusBadge(invoice.status)}>
                {invoice.status}
              </span>
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Issued on {formatDate(invoice.date)} • Due on {formatDate(invoice.dueDate)}
              {invoice.status === 'Paid' && invoice.paymentDate && (
                <span className="ml-2 text-green-600 dark:text-green-400">
                  • Paid on {formatDate(invoice.paymentDate)}
                </span>
              )}
            </div>
          </div>
          <div className="flex space-x-2 w-full sm:w-auto">
            {invoice.status === 'Pending' && (
              <Button className="bg-green-600 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-700 flex items-center">
                <FiCreditCard className="h-4 w-4 mr-2" />
                Record Payment
              </Button>
            )}
            <Button variant="outline" size="sm" className="flex items-center">
              <FiEdit className="h-4 w-4 mr-2" />
              Edit
            </Button>
            <Button variant="outline" size="sm" className="flex items-center text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 border-red-200 dark:border-red-800 hover:bg-red-50 dark:hover:bg-red-900/20">
              <FiCreditCard className="h-4 w-4 mr-2" />
              Void
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main content */}
        <div className="lg:col-span-3 space-y-6">
          {/* Tabs */}
          <div className="border-b border-gray-200 dark:border-slate-700">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('details')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${activeTab === 'details' 
                  ? 'border-[#18546c] text-[#18546c] dark:border-blue-400 dark:text-blue-400' 
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'}`}
              >
                Invoice Details
              </button>
              <button
                onClick={() => setActiveTab('activity')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${activeTab === 'activity' 
                  ? 'border-[#18546c] text-[#18546c] dark:border-blue-400 dark:text-blue-400' 
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'}`}
              >
                Activity
              </button>
              <button
                onClick={() => setActiveTab('documents')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${activeTab === 'documents' 
                  ? 'border-[#18546c] text-[#18546c] dark:border-blue-400 dark:text-blue-400' 
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'}`}
              >
                Documents
              </button>
            </nav>
          </div>

          {/* Tab content */}
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 overflow-hidden">
            {activeTab === 'details' && (
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Items</h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-slate-700">
                    <thead className="bg-gray-50 dark:bg-slate-700/50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Item
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Quantity
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Unit Price
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Total
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-slate-700">
                      {invoice.items.map((item: any) => (
                        <tr key={item.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">{item.name}</div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">{item.description}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-right">
                            {item.quantity}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-right">
                            {formatCurrency(item.unitPrice)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100 text-right">
                            {formatCurrency(item.total)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr>
                        <td colSpan={3} className="text-right px-6 py-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                          Subtotal
                        </td>
                        <td className="px-6 py-2 text-right text-sm font-medium text-gray-900 dark:text-gray-100">
                          {formatCurrency(invoice.subtotal)}
                        </td>
                      </tr>
                      <tr>
                        <td colSpan={3} className="text-right px-6 py-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                          Tax (10%)
                        </td>
                        <td className="px-6 py-2 text-right text-sm font-medium text-gray-900 dark:text-gray-100">
                          {formatCurrency(invoice.tax)}
                        </td>
                      </tr>
                      <tr>
                        <td colSpan={3} className="px-6 py-3 text-right text-base font-bold text-gray-900 dark:text-gray-100 border-t border-gray-200 dark:border-slate-700">
                          Total
                        </td>
                        <td className="px-6 py-3 text-right text-base font-bold text-gray-900 dark:text-white border-t border-gray-200 dark:border-slate-700">
                          {formatCurrency(invoice.total)}
                        </td>
                      </tr>
                    </tfoot>
                  </table>
                </div>

                <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">PAYMENT METHOD</h3>
                    <p className="text-sm text-gray-900 dark:text-gray-100">{invoice.paymentMethod}</p>
                    {invoice.status === 'Paid' && invoice.paymentDate && (
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        Paid on {formatDate(invoice.paymentDate)}
                      </p>
                    )}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">TERMS & CONDITIONS</h3>
                    <p className="text-sm text-gray-900 dark:text-gray-100">{invoice.terms}</p>
                  </div>
                </div>

                {invoice.notes && (
                  <div className="mt-6 pt-6 border-t border-gray-200 dark:border-slate-700">
                    <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">NOTES</h3>
                    <p className="text-sm text-gray-900 dark:text-gray-100">{invoice.notes}</p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'activity' && (
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Activity Log</h3>
                <div className="flow-root">
                  <ul className="-mb-8">
                    {invoice.history.map((activity: any, index: number) => (
                      <li key={activity.id}>
                        <div className="relative pb-8">
                          {index !== invoice.history.length - 1 ? (
                            <span className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-slate-600" aria-hidden="true"></span>
                          ) : null}
                          <div className="relative flex space-x-3">
                            <div>
                              <span className="h-8 w-8 rounded-full bg-blue-500 dark:bg-blue-600 flex items-center justify-center ring-8 ring-white dark:ring-slate-800">
                                <FiFileText className="h-4 w-4 text-white" />
                              </span>
                            </div>
                            <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                              <div>
                                <p className="text-sm text-gray-700 dark:text-gray-300">
                                  <span className="font-medium text-gray-900 dark:text-white">{activity.user}</span> {activity.description}
                                </p>
                              </div>
                              <div className="text-right text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                                <time dateTime={activity.date}>{formatDateTime(activity.date)}</time>
                              </div>
                            </div>
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}

            {activeTab === 'documents' && (
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Related Documents</h3>
                <div className="bg-white dark:bg-slate-800 overflow-hidden sm:rounded-md">
                  <ul className="divide-y divide-gray-200 dark:divide-slate-700">
                    <li>
                      <div className="px-4 py-4 flex items-center sm:px-6">
                        <div className="min-w-0 flex-1 sm:flex sm:items-center sm:justify-between">
                          <div className="truncate">
                            <div className="flex text-sm">
                              <p className="font-medium text-blue-600 dark:text-blue-400 truncate">Invoice_{invoice.id}.pdf</p>
                              <p className="ml-1 flex-shrink-0 font-normal text-gray-500 dark:text-gray-400">PDF</p>
                            </div>
                            <div className="mt-2 flex">
                              <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                <p>Generated on {formatDate(invoice.date)}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="ml-5 flex-shrink-0">
                          <Button variant="outline" size="sm" className="flex items-center">
                            <FiDownload className="h-4 w-4 mr-2" />
                            Download
                          </Button>
                        </div>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Customer Information */}
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 overflow-hidden">
            <div className="px-6 py-5 border-b border-gray-200 dark:border-slate-700">
              <h3 className="text-base font-medium text-gray-900 dark:text-gray-100">Customer Information</h3>
            </div>
            <div className="px-6 py-5">
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">Customer</h4>
                  <p className="mt-1 text-sm text-gray-900 dark:text-gray-100">{invoice.customer}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">ID: {invoice.customerId}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">Email</h4>
                  <p className="mt-1 text-sm text-gray-900 dark:text-gray-100"><EMAIL></p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</h4>
                  <p className="mt-1 text-sm text-gray-900 dark:text-gray-100">+****************</p>
                </div>
              </div>
            </div>
          </div>

          {/* Billing Address */}
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 overflow-hidden">
            <div className="px-6 py-5 border-b border-gray-200 dark:border-slate-700">
              <h3 className="text-base font-medium text-gray-900 dark:text-gray-100">Billing Address</h3>
            </div>
            <div className="px-6 py-5">
              <address className="not-italic text-sm text-gray-900 dark:text-gray-100">
                <div>{invoice.billingAddress.name}</div>
                {invoice.billingAddress.company && (
                  <div>{invoice.billingAddress.company}</div>
                )}
                <div>{invoice.billingAddress.street}</div>
                <div>{invoice.billingAddress.city}, {invoice.billingAddress.state} {invoice.billingAddress.zip}</div>
                <div>{invoice.billingAddress.country}</div>
              </address>
            </div>
          </div>

          {/* Shipping Address */}
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 overflow-hidden">
            <div className="px-6 py-5 border-b border-gray-200 dark:border-slate-700">
              <h3 className="text-base font-medium text-gray-900 dark:text-gray-100">Shipping Address</h3>
            </div>
            <div className="px-6 py-5">
              <address className="not-italic text-sm text-gray-900 dark:text-gray-100">
                <div>{invoice.shippingAddress.name}</div>
                {invoice.shippingAddress.company && (
                  <div>{invoice.shippingAddress.company}</div>
                )}
                <div>{invoice.shippingAddress.street}</div>
                <div>{invoice.shippingAddress.city}, {invoice.shippingAddress.state} {invoice.shippingAddress.zip}</div>
                <div>{invoice.shippingAddress.country}</div>
              </address>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SalesInvoiceDetailPage;

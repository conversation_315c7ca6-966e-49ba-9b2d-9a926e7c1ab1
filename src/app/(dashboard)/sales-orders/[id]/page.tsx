'use client';

import React from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { FiArrowLeft, FiEdit, FiPrinter, FiShare2, FiDownload, FiFileText, FiCalendar, FiDollarSign, FiUser, FiTag } from 'react-icons/fi';
import { Loading } from '@/components/global/Loading';

// Mock data for sales order details
const mockSalesOrder = {
  id: 'SO-2023-001',
  customer: 'ABC Corporation',
  date: '2023-10-15',
  dueDate: '2023-11-14',
  status: 'Pending',
  currency: 'USD',
  subtotal: 11250.00,
  tax: 1250.00,
  total: 12500.00,
  notes: 'Please ensure delivery is made before the due date.',
  items: [
    {
      id: 1,
      name: 'Office Chair',
      description: 'Ergonomic office chair with lumbar support',
      quantity: 5,
      unitPrice: 1500.00,
      total: 7500.00
    },
    {
      id: 2,
      name: 'Standing Desk',
      description: 'Adjustable height standing desk',
      quantity: 2,
      unitPrice: 1875.00,
      total: 3750.00
    },
    {
      id: 3,
      name: 'Monitor Arm',
      description: 'Dual monitor arm mount',
      quantity: 5,
      unitPrice: 200.00,
      total: 1000.00
    }
  ]
};

const SalesOrderDetailsPage = () => {
  const { id } = useParams();
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = React.useState(true);
  const [order, setOrder] = React.useState<any>(null);

  React.useEffect(() => {
    if (isAuthenticated === false) {
      router.push('/login');
    } else if (isAuthenticated === true) {
      // Simulate API call to fetch order details
      const timer = setTimeout(() => {
        setOrder(mockSalesOrder);
        setIsLoading(false);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [isAuthenticated, router, id]);

  const getStatusBadge = (status: string) => {
    const baseClasses = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium';
    switch (status) {
      case 'Pending':
        return `${baseClasses} bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400`;
      case 'In Progress':
        return `${baseClasses} bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400`;
      case 'Completed':
        return `${baseClasses} bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300`;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: order?.currency || 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  if (!isAuthenticated || isLoading || !order) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Loading fullScreen={false} text="Loading sales order details..." size="lg" className="py-12" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700"
        >
          <FiArrowLeft className="mr-2 h-4 w-4" />
          Back to Sales Orders
        </Button>
      </div>

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 flex items-center">
            Sales Order #{order.id}
            <span className={`ml-3 ${getStatusBadge(order.status)}`}>
              {order.status}
            </span>
          </h1>
          <p className="text-gray-500 dark:text-gray-400 mt-1">
            Created on {formatDate(order.date)}
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-2">
          <Button variant="outline" className="flex items-center">
            <FiShare2 className="mr-2 h-4 w-4" />
            Share
          </Button>
          <Button variant="outline" className="flex items-center">
            <FiPrinter className="mr-2 h-4 w-4" />
            Print
          </Button>
          <Button variant="outline" className="flex items-center">
            <FiDownload className="mr-2 h-4 w-4" />
            Download
          </Button>
          <Button className="bg-[#18546c] hover:bg-[#134048] dark:bg-[#18546c] dark:hover:bg-[#134048] flex items-center">
            <FiEdit className="mr-2 h-4 w-4" />
            Edit
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Order Summary */}
        <div className="lg:col-span-2">
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 p-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Order Summary</h2>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-slate-700">
                <thead>
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Item
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Qty
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Unit Price
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Total
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-slate-700">
                  {order.items.map((item: any) => (
                    <tr key={item.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">{item.name}</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">{item.description}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-right">
                        {item.quantity}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-right">
                        {formatCurrency(item.unitPrice)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100 text-right">
                        {formatCurrency(item.total)}
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr>
                    <td colSpan={3} className="px-6 py-3 text-right text-sm font-medium text-gray-500 dark:text-gray-400">
                      Subtotal
                    </td>
                    <td className="px-6 py-3 text-right text-sm font-medium text-gray-900 dark:text-gray-100">
                      {formatCurrency(order.subtotal)}
                    </td>
                  </tr>
                  <tr>
                    <td colSpan={3} className="px-6 py-1 text-right text-sm font-medium text-gray-500 dark:text-gray-400">
                      Tax (10%)
                    </td>
                    <td className="px-6 py-1 text-right text-sm font-medium text-gray-900 dark:text-gray-100">
                      {formatCurrency(order.tax)}
                    </td>
                  </tr>
                  <tr>
                    <td colSpan={3} className="px-6 py-3 text-right text-base font-bold text-gray-900 dark:text-gray-100 border-t border-gray-200 dark:border-slate-700">
                      Total
                    </td>
                    <td className="px-6 py-3 text-right text-base font-bold text-gray-900 dark:text-gray-100 border-t border-gray-200 dark:border-slate-700">
                      {formatCurrency(order.total)}
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>

            {order.notes && (
              <div className="mt-6 pt-6 border-t border-gray-200 dark:border-slate-700">
                <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Notes</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">{order.notes}</p>
              </div>
            )}
          </div>
        </div>

        {/* Order Details */}
        <div className="space-y-6">
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 p-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Order Details</h2>
            
            <div className="space-y-4">
              <div className="flex items-start">
                <FiFileText className="h-5 w-5 text-gray-400 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Order Number</p>
                  <p className="text-sm text-gray-900 dark:text-gray-100">{order.id}</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <CalendarIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Order Date</p>
                  <p className="text-sm text-gray-900 dark:text-gray-100">{formatDate(order.date)}</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <FiCalendar className="h-5 w-5 text-gray-400 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Due Date</p>
                  <p className="text-sm text-gray-900 dark:text-gray-100">{formatDate(order.dueDate)}</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <FiDollarSign className="h-5 w-5 text-gray-400 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Amount</p>
                  <p className="text-lg font-bold text-gray-900 dark:text-gray-100">{formatCurrency(order.total)}</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <FiTag className="h-5 w-5 text-gray-400 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Status</p>
                  <span className={getStatusBadge(order.status)}>
                    {order.status}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 p-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Customer</h2>
            
            <div className="flex items-start">
              <FiUser className="h-5 w-5 text-gray-400 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{order.customer}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Customer since 2022</p>
                
                <div className="mt-3 pt-3 border-t border-gray-200 dark:border-slate-700">
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Contact Information</h3>
                  <p className="text-sm text-gray-900 dark:text-gray-100"><EMAIL></p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">+1 (555) 123-4567</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Calendar icon component since it's not included in react-icons/fi
const CalendarIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
    <line x1="16" y1="2" x2="16" y2="6"></line>
    <line x1="8" y1="2" x2="8" y2="6"></line>
    <line x1="3" y1="10" x2="21" y2="10"></line>
  </svg>
);

export default SalesOrderDetailsPage;

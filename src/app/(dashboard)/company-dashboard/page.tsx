'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import CompanyDashboard from '@/components/dashboard/CompanyDashboard';

export default function CompanyDashboardPage() {
  const { user, isAuthenticated, logout } = useAuth();
  const router = useRouter();

  React.useEffect(() => {
    if (isAuthenticated === false) {
      router.push('/login');
    } else if (isAuthenticated === true && user) {
      // Only allow access if user is a company admin
      if (!user.roles || !user.roles.includes('c_admin')) {
        // If user is not a company admin, redirect to login
        alert('You do not have permission to access this page.');
        router.push('/login');
        return;
      }
    }
  }, [isAuthenticated, router, user]);

  if (!isAuthenticated || !user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return <CompanyDashboard user={user} />;
}

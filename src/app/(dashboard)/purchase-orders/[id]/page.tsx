'use client';

import React from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { FiArrowLeft, FiPrinter, FiMail, FiDownload, FiEdit, FiTrash2, FiChevronDown } from 'react-icons/fi';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

// Mock data for the purchase order
const mockPurchaseOrder = {
  id: 'PO-2023-001',
  date: '2023-10-15',
  dueDate: '2023-11-15',
  vendor: 'Tech Supplies Inc.',
  vendorAddress: '123 Tech Street, San Francisco, CA 94103, USA',
  vendorEmail: '<EMAIL>',
  vendorPhone: '+****************',
  reference: 'REF-PO-001',
  status: 'Approved',
  currency: 'USD',
  subtotal: 11500.00,
  tax: 1000.00,
  total: 12500.00,
  notes: 'Please include the PO number on all invoices and shipping documents.',
  items: [
    {
      id: 1,
      description: 'Laptop - Dell XPS 15',
      quantity: 5,
      unitPrice: 2000.00,
      total: 10000.00,
    },
    {
      id: 2,
      description: 'Docking Station - USB-C',
      quantity: 5,
      unitPrice: 150.00,
      total: 750.00,
    },
    {
      id: 3,
      description: 'Wireless Mouse - Logitech MX Master 3',
      quantity: 5,
      unitPrice: 100.00,
      total: 500.00,
    },
  ],
  shippingAddress: 'Ascension Inc.\n456 Office Park Drive\nSuite 200\nSan Francisco, CA 94103\nUSA',
  billingAddress: 'Ascension Inc.\nFinance Department\n456 Office Park Drive\nSuite 200\nSan Francisco, CA 94103\nUSA',
  terms: 'Net 30',
  shippingMethod: 'Standard Ground',
  requestedBy: 'John Smith',
  department: 'IT',
  project: 'Office Equipment Refresh',
  budgetCode: 'IT-2023-EQ',
};

const statusVariantMap = {
  'Approved': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
  'Pending': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
  'Draft': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
  'Rejected': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
};

export default function PurchaseOrderDetailPage() {
  const params = useParams();
  const poId = params?.id;
  const po = mockPurchaseOrder; // In a real app, fetch by poId

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: po.currency || 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  return (
    <div className="p-6 bg-gray-50 dark:bg-slate-900 min-h-screen">
      {/* Header with back button and actions */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div className="flex items-center">
          <Link 
            href="/purchase-orders" 
            className="flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 mr-4"
          >
            <FiArrowLeft className="mr-2 h-4 w-4" />
            Back to Purchase Orders
          </Link>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {po.id} - {po.vendor}
          </h1>
          <Badge 
            className={`ml-4 text-xs font-medium px-2 py-1 rounded-full ${
              statusVariantMap[po.status as keyof typeof statusVariantMap] || 
              'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
            }`}
          >
            {po.status}
          </Badge>
        </div>
        <div className="flex flex-wrap gap-2 w-full sm:w-auto">
          <Button variant="outline" size="sm" className="flex items-center gap-2">
            <FiPrinter className="h-4 w-4" />
            Print
          </Button>
          <Button variant="outline" size="sm" className="flex items-center gap-2">
            <FiMail className="h-4 w-4" />
            Email
          </Button>
          <Button variant="outline" size="sm" className="flex items-center gap-2">
            <FiDownload className="h-4 w-4" />
            PDF
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="flex items-center gap-2">
                More
                <FiChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem className="flex items-center gap-2">
                <FiEdit className="h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem className="flex items-center gap-2 text-red-600">
                <FiTrash2 className="h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button className="bg-[#18546c] hover:bg-[#0d3d50] text-white">
            Approve
          </Button>
        </div>
      </div>

      {/* PO Details */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        {/* Left Column */}
        <div className="lg:col-span-2 space-y-6">
          {/* Vendor and Shipping Info */}
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Vendor</h3>
                <p className="font-medium text-gray-900 dark:text-gray-100">{po.vendor}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{po.vendorAddress}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">{po.vendorEmail}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">{po.vendorPhone}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Shipping Address</h3>
                <p className="whitespace-pre-line text-gray-900 dark:text-gray-100">{po.shippingAddress}</p>
              </div>
            </div>
          </div>

          {/* PO Items */}
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 overflow-hidden">
            <div className="p-6">
              <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Items</h2>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-slate-700">
                  <thead>
                    <tr className="bg-gray-50 dark:bg-slate-700">
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Description
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Qty
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Unit Price
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Total
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-slate-700">
                    {po.items.map((item) => (
                      <tr key={item.id} className="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                          {item.description}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-right">
                          {item.quantity}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-right">
                          {formatCurrency(item.unitPrice)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100 text-right">
                          {formatCurrency(item.total)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Totals */}
            <div className="border-t border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800 px-6 py-4">
              <div className="flex justify-between max-w-md ml-auto">
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  <p className="mb-1">Subtotal</p>
                  <p className="mb-1">Tax (8.7%)</p>
                  <p className="font-medium text-gray-900 dark:text-gray-100">Total</p>
                </div>
                <div className="text-right">
                  <p className="mb-1">{formatCurrency(po.subtotal)}</p>
                  <p className="mb-1">{formatCurrency(po.tax)}</p>
                  <p className="font-medium text-gray-900 dark:text-gray-100">{formatCurrency(po.total)}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Notes */}
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 p-6">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Notes</h3>
            <p className="text-gray-700 dark:text-gray-300">{po.notes}</p>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          {/* PO Summary */}
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 p-6">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-4">Purchase Order Summary</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-500 dark:text-gray-400">PO Number</span>
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{po.id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500 dark:text-gray-400">Date Created</span>
                <span className="text-sm text-gray-900 dark:text-gray-100">
                  {new Date(po.date).toLocaleDateString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500 dark:text-gray-400">Due Date</span>
                <span className="text-sm text-gray-900 dark:text-gray-100">
                  {new Date(po.dueDate).toLocaleDateString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500 dark:text-gray-400">Payment Terms</span>
                <span className="text-sm text-gray-900 dark:text-gray-100">{po.terms}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500 dark:text-gray-400">Shipping Method</span>
                <span className="text-sm text-gray-900 dark:text-gray-100">{po.shippingMethod}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500 dark:text-gray-400">Requested By</span>
                <span className="text-sm text-gray-900 dark:text-gray-100">{po.requestedBy}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500 dark:text-gray-400">Department</span>
                <span className="text-sm text-gray-900 dark:text-gray-100">{po.department}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500 dark:text-gray-400">Project</span>
                <span className="text-sm text-gray-900 dark:text-gray-100">{po.project}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500 dark:text-gray-400">Budget Code</span>
                <span className="text-sm text-gray-900 dark:text-gray-100">{po.budgetCode}</span>
              </div>
            </div>
          </div>

          {/* Billing Address */}
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 p-6">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Billing Address</h3>
            <p className="whitespace-pre-line text-gray-900 dark:text-gray-100">{po.billingAddress}</p>
          </div>

          {/* Activity */}
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 p-6">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-4">Activity</h3>
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="bg-blue-100 dark:bg-blue-900/30 rounded-full p-2 mr-3">
                  <svg className="h-4 w-4 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">Purchase order created</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {new Date(po.date).toLocaleDateString()} at {new Date(po.date).toLocaleTimeString()}
                  </p>
                </div>
              </div>
              <div className="flex items-start">
                <div className="bg-green-100 dark:bg-green-900/30 rounded-full p-2 mr-3">
                  <svg className="h-4 w-4 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">Approved by Finance</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {new Date(po.date).toLocaleDateString()} at {new Date(po.date).toLocaleTimeString()}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

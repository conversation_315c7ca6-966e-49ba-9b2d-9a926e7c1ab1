'use client';

import React, { Suspense, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { FiSearch, FiFilter, FiChevronDown, FiChevronUp, FiFileText, FiDownload } from 'react-icons/fi';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';

// Mock data for the table
const mockPurchaseOrders = [
  {
    id: 'PO-2023-001',
    date: '2023-10-15',
    vendor: 'Tech Supplies Inc.',
    reference: 'REF-PO-001',
    status: 'Approved',
    dueDate: '2023-11-15',
    amount: 12500.00,
    currency: 'USD',
    items: 5,
  },
  {
    id: 'PO-2023-002',
    date: '2023-10-18',
    vendor: 'Office Essentials Co.',
    reference: 'REF-PO-002',
    status: 'Pending',
    dueDate: '2023-11-18',
    amount: 8450.50,
    currency: 'USD',
    items: 3,
  },
  {
    id: 'PO-2023-003',
    date: '2023-10-20',
    vendor: 'Software Solutions Ltd',
    reference: 'REF-PO-003',
    status: 'Approved',
    dueDate: '2023-11-20',
    amount: 3200.00,
    currency: 'USD',
    items: 2,
  },
  {
    id: 'PO-2023-004',
    date: '2023-10-22',
    vendor: 'Furniture World',
    reference: 'REF-PO-004',
    status: 'Draft',
    dueDate: '2023-11-22',
    amount: 15750.75,
    currency: 'USD',
    items: 8,
  },
  {
    id: 'PO-2023-005',
    date: '2023-10-25',
    vendor: 'IT Hardware Inc',
    reference: 'REF-PO-005',
    status: 'Approved',
    dueDate: '2023-11-25',
    amount: 9200.00,
    currency: 'USD',
    items: 4,
  },
];

const statusVariantMap = {
  'Approved': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
  'Pending': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
  'Draft': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
  'Rejected': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
};

export default function PurchaseOrdersPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    }>
      <PurchaseOrdersContent />
    </Suspense>
  );
}

function PurchaseOrdersContent() {
  const searchParams = useSearchParams();
  const statusFilter = searchParams.get('status');
  
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' } | null>(null);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [activeFilter, setActiveFilter] = useState<string | null>(statusFilter || 'All');

  const handleSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const sortedPurchaseOrders = [...mockPurchaseOrders].sort((a, b) => {
    if (!sortConfig) return 0;
    
    const aValue = a[sortConfig.key as keyof typeof a];
    const bValue = b[sortConfig.key as keyof typeof b];
    
    if (aValue < bValue) {
      return sortConfig.direction === 'asc' ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortConfig.direction === 'asc' ? 1 : -1;
    }
    return 0;
  });

  const filteredPurchaseOrders = sortedPurchaseOrders.filter(po => {
    const searchLower = searchTerm.toLowerCase();
    const matchesSearch = 
      po.id.toLowerCase().includes(searchLower) ||
      po.vendor.toLowerCase().includes(searchLower) ||
      po.reference.toLowerCase().includes(searchLower);
      
    const matchesStatus = activeFilter === 'All' || po.status === activeFilter;
    
    return matchesSearch && matchesStatus;
  });

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  return (
    <div className="p-6 bg-gray-50 dark:bg-slate-900 min-h-screen">
      {/* Breadcrumb */}
      <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-4">
        <Link href="/dashboard" className="hover:text-gray-700 dark:hover:text-gray-300">
          Dashboard
        </Link>
        <span className="mx-2">/</span>
        <span className="text-gray-700 dark:text-gray-300">Purchase Orders</span>
      </div>

      {/* Page Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {statusFilter ? `${statusFilter} Purchase Orders` : 'Purchase Orders'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {statusFilter ? `Showing ${statusFilter.toLowerCase()} purchase orders` : 'Manage your purchase orders'}
          </p>
        </div>
        <div className="flex gap-3 w-full sm:w-auto">
          <Button variant="outline" className="flex items-center gap-2">
            <FiDownload className="h-4 w-4" />
            Export
          </Button>
          <Button className="bg-[#18546c] hover:bg-[#0d3d50] text-white">
            + Create New
          </Button>
        </div>
      </div>

      {/* Action Bar */}
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 p-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              placeholder="Search by PO #, vendor, or reference..."
              className="pl-10 w-full"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          {statusFilter && (
            <div className="flex items-center">
              <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                Status: {statusFilter}
              </Badge>
              <button 
                onClick={() => {
                  setActiveFilter('All');
                  window.history.pushState({}, '', '/purchase-orders');
                }}
                className="ml-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm"
              >
                Clear
              </button>
            </div>
          )}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <FiFilter className="h-4 w-4" />
                Filter
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56">
              <DropdownMenuItem onClick={() => setActiveFilter('All')}>
                Status: All
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setActiveFilter('Approved')}>
                Status: Approved
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setActiveFilter('Pending')}>
                Status: Pending
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setActiveFilter('Draft')}>
                Status: Draft
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setActiveFilter('Rejected')}>
                Status: Rejected
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <span>Sort by</span>
                {sortConfig?.direction === 'asc' ? (
                  <FiChevronUp className="h-4 w-4" />
                ) : (
                  <FiChevronDown className="h-4 w-4" />
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => handleSort('date')}>
                Date
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleSort('dueDate')}>
                Due Date
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleSort('amount')}>
                Amount
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleSort('status')}>
                Status
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Data Table */}
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50 dark:bg-slate-700">
                <TableHead className="font-medium text-gray-700 dark:text-gray-300">
                  <button 
                    className="flex items-center gap-1"
                    onClick={() => handleSort('id')}
                  >
                    PO #
                    {sortConfig?.key === 'id' && (
                      sortConfig.direction === 'asc' ? 
                      <FiChevronUp className="h-4 w-4" /> : 
                      <FiChevronDown className="h-4 w-4" />
                    )}
                  </button>
                </TableHead>
                <TableHead className="font-medium text-gray-700 dark:text-gray-300">
                  <button 
                    className="flex items-center gap-1"
                    onClick={() => handleSort('date')}
                  >
                    Date
                    {sortConfig?.key === 'date' && (
                      sortConfig.direction === 'asc' ? 
                      <FiChevronUp className="h-4 w-4" /> : 
                      <FiChevronDown className="h-4 w-4" />
                    )}
                  </button>
                </TableHead>
                <TableHead className="font-medium text-gray-700 dark:text-gray-300">
                  <button 
                    className="flex items-center gap-1"
                    onClick={() => handleSort('vendor')}
                  >
                    Vendor
                    {sortConfig?.key === 'vendor' && (
                      sortConfig.direction === 'asc' ? 
                      <FiChevronUp className="h-4 w-4" /> : 
                      <FiChevronDown className="h-4 w-4" />
                    )}
                  </button>
                </TableHead>
                <TableHead className="font-medium text-gray-700 dark:text-gray-300">Reference</TableHead>
                <TableHead className="font-medium text-gray-700 dark:text-gray-300">
                  <button 
                    className="flex items-center gap-1"
                    onClick={() => handleSort('status')}
                  >
                    Status
                    {sortConfig?.key === 'status' && (
                      sortConfig.direction === 'asc' ? 
                      <FiChevronUp className="h-4 w-4" /> : 
                      <FiChevronDown className="h-4 w-4" />
                    )}
                  </button>
                </TableHead>
                <TableHead className="font-medium text-gray-700 dark:text-gray-300">Due Date</TableHead>
                <TableHead className="text-right font-medium text-gray-700 dark:text-gray-300">
                  <button 
                    className="flex items-center gap-1 ml-auto"
                    onClick={() => handleSort('amount')}
                  >
                    Amount
                    {sortConfig?.key === 'amount' && (
                      sortConfig.direction === 'asc' ? 
                      <FiChevronUp className="h-4 w-4" /> : 
                      <FiChevronDown className="h-4 w-4" />
                    )}
                  </button>
                </TableHead>
                <TableHead className="text-center font-medium text-gray-700 dark:text-gray-300">Items</TableHead>
                <TableHead className="w-10"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredPurchaseOrders.length > 0 ? (
                filteredPurchaseOrders.map((po) => (
                  <TableRow key={po.id} className="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                    <TableCell className="font-medium">
                      <Link 
                        href={`/purchase-orders/${po.id}`}
                        className="text-blue-600 hover:underline dark:text-blue-400"
                      >
                        {po.id}
                      </Link>
                    </TableCell>
                    <TableCell>{new Date(po.date).toLocaleDateString()}</TableCell>
                    <TableCell className="max-w-xs truncate">{po.vendor}</TableCell>
                    <TableCell>{po.reference}</TableCell>
                    <TableCell>
                      <Badge 
                        className={`text-xs font-medium px-2 py-1 rounded-full ${
                          statusVariantMap[po.status as keyof typeof statusVariantMap] || 
                          'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                        }`}
                      >
                        {po.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{new Date(po.dueDate).toLocaleDateString()}</TableCell>
                    <TableCell className="text-right">
                      {formatCurrency(po.amount, po.currency)}
                    </TableCell>
                    <TableCell className="text-center">{po.items}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <FiChevronDown className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>View</DropdownMenuItem>
                          <DropdownMenuItem>Edit</DropdownMenuItem>
                          <DropdownMenuItem>Duplicate</DropdownMenuItem>
                          <DropdownMenuItem className="text-red-600">Delete</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8 text-gray-500 dark:text-gray-400">
                    <div className="flex flex-col items-center justify-center space-y-2">
                      <FiFileText className="h-12 w-12 text-gray-300 dark:text-gray-600" />
                      <p>No purchase orders found</p>
                      <p className="text-sm">Try adjusting your search or filter criteria</p>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        <div className="px-6 py-4 border-t border-gray-200 dark:border-slate-700 flex flex-col sm:flex-row items-center justify-between gap-4">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Showing <span className="font-medium">1</span> to{' '}
            <span className="font-medium">{Math.min(rowsPerPage, filteredPurchaseOrders.length)}</span> of{' '}
            <span className="font-medium">{filteredPurchaseOrders.length}</span> entries
          </div>
          
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500 dark:text-gray-400">Rows per page:</span>
            <select
              className="bg-white dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-md px-2 py-1 text-sm"
              value={rowsPerPage}
              onChange={(e) => setRowsPerPage(Number(e.target.value))}
            >
              {[10, 25, 50, 100].map((size) => (
                <option key={size} value={size}>
                  {size}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-center space-x-2">
            <button
              className="px-3 py-1 border border-gray-200 dark:border-slate-600 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={true}
            >
              Previous
            </button>
            <button className="px-3 py-1 bg-blue-600 text-white rounded-md text-sm font-medium">
              1
            </button>
            <button
              className="px-3 py-1 border border-gray-200 dark:border-slate-600 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={true}
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

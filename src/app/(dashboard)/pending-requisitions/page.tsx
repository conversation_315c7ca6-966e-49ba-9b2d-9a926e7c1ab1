'use client';

import React, { Suspense, useState } from 'react';
import { useSearchPara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { MagnifyingGlassIcon, FunnelIcon, ChevronDownIcon, ChevronUpIcon, DocumentTextIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';

// Mock data for the table
const mockRequisitions = [
  {
    id: '0002',
    year: '2024',
    caseNo: '173',
    reference: 'UNRA/SUPLS/24-25/173',
    subject: 'Spare parts vehicle UAO 257R',
    status: 'Requisition',
    dateCreated: 'Oct 14, 2024',
    dateRequired: 'Sep 18, 2024',
    budgetCode: '228002',
    budgetCategory: 'Maintenance vehicles',
    description: 'Spare parts for vehicle UAO 257R',
    requestedBy: 'Brian Gimel',
    contactPerson: 'Anne Kalungi, 0777074589',
    department: 'Mechanical',
    priority: 'Urgent',
    deliveryInstructions: 'Toyota Arua, 15 Arua Road',
    isFavorite: true,
    lineItems: [
      {
        id: '01',
        description: 'Diesel Filter FF5880',
        partNumber: 'FF5880',
        unit: 'pcs',
        quantity: 30,
        hasAttachment: true,
        comments: 'Preferably from Toyota'
      },
      {
        id: '02',
        description: 'Oil Filter LF5350',
        partNumber: 'LF5350',
        unit: 'pairs',
        quantity: 40,
        hasAttachment: true,
        comments: 'Preferably from Toyota'
      },
      {
        id: '03',
        description: 'Air Filter AF2350',
        partNumber: 'PJ1050',
        unit: 'pcs',
        quantity: 20,
        hasAttachment: true,
        comments: 'Fuel-efficient model'
      }
    ]
  },
  {
    id: '0003',
    year: '2024',
    caseNo: '179',
    reference: 'UNRA/SUPLS/24-25/179',
    subject: 'Lawn mower',
    status: 'Requisition',
    dateCreated: 'Oct 12, 2024',
    dateRequired: 'Oct 30, 2024',
    budgetCode: '228003',
    budgetCategory: 'Maintenance machinery',
    description: 'Industrial lawn mower for headquarters compound',
    requestedBy: 'Sarah Johnson',
    contactPerson: 'Mark Peters, 0788123456',
    department: 'Facilities',
    priority: 'Normal',
    deliveryInstructions: 'Main Warehouse, Plot 45 Industrial Area',
    isFavorite: false,
    lineItems: [
      {
        id: '01',
        description: 'Industrial Lawn Mower 5HP',
        partNumber: 'LM5000',
        unit: 'unit',
        quantity: 1,
        hasAttachment: true,
        comments: 'With spare blades'
      }
    ]
  },
  {
    id: '0005',
    year: '2024',
    caseNo: '178',
    reference: 'UNRA/SUPLS/24-25/178',
    subject: 'Water pump spares',
    status: 'Requisition',
    dateCreated: 'Oct 11, 2024',
    dateRequired: 'Nov 5, 2024',
    budgetCode: '228004',
    budgetCategory: 'Construction equipment',
    description: 'Replacement parts for water pump at northern facility',
    requestedBy: 'David Mukasa',
    contactPerson: 'Robert Kigozi, 0756789012',
    department: 'Engineering',
    priority: 'High',
    deliveryInstructions: 'Northern Facility, Plot 12 Gulu Road',
    isFavorite: false,
    lineItems: [
      {
        id: '01',
        description: 'Water Pump Impeller',
        partNumber: 'WPI-450',
        unit: 'pcs',
        quantity: 2,
        hasAttachment: false,
        comments: 'For model WP-2000'
      },
      {
        id: '02',
        description: 'Pump Shaft Seal',
        partNumber: 'PSS-120',
        unit: 'pcs',
        quantity: 5,
        hasAttachment: true,
        comments: 'Silicon type'
      }
    ]
  },
  {
    id: '0006',
    year: '2024',
    caseNo: '177',
    reference: 'UNRA/SUPLS/24-25/177',
    subject: 'Carburetor assembly',
    status: 'Requisition',
    dateCreated: 'Oct 13, 2024',
    dateRequired: 'Oct 25, 2024',
    budgetCode: '228005',
    budgetCategory: 'Maintenance machinery',
    description: 'Replacement carburetor for generator set',
    requestedBy: 'Patricia Nambi',
    contactPerson: 'James Ochieng, 0701234567',
    department: 'Operations',
    priority: 'Normal',
    deliveryInstructions: 'Main Workshop, Industrial Area',
    isFavorite: true,
    lineItems: [
      {
        id: '01',
        description: 'Carburetor Assembly Complete',
        partNumber: 'CA-G500',
        unit: 'set',
        quantity: 1,
        hasAttachment: true,
        comments: 'For Generator Model G-5000'
      },
      {
        id: '02',
        description: 'Fuel Line',
        partNumber: 'FL-250',
        unit: 'meters',
        quantity: 3,
        hasAttachment: false,
        comments: '8mm diameter'
      }
    ]
  },
  {
    id: '0007',
    year: '2023',
    caseNo: 'C-003',
    reference: 'UNRA/SUPLS/23-24/003',
    subject: 'Office Furniture',
    status: 'Pending',
    dateCreated: 'Dec 17, 2023',
    dateRequired: 'Jan 15, 2024',
    budgetCode: 'BGT-2023-OFF',
    budgetCategory: 'Office Expenses',
    description: 'New furniture for conference room',
    requestedBy: 'Elizabeth Nantume',
    contactPerson: 'Peter Okello, 0772345678',
    department: 'Administration',
    priority: 'Low',
    deliveryInstructions: 'Head Office, 3rd Floor',
    isFavorite: false,
    lineItems: [
      {
        id: '01',
        description: 'Conference Table',
        partNumber: 'CT-12',
        unit: 'pcs',
        quantity: 1,
        hasAttachment: true,
        comments: '12-seater, oak finish'
      },
      {
        id: '02',
        description: 'Executive Chairs',
        partNumber: 'EC-01',
        unit: 'pcs',
        quantity: 12,
        hasAttachment: true,
        comments: 'Black leather, adjustable height'
      }
    ]
  },
  {
    id: '0008',
    year: '2023',
    caseNo: 'C-004',
    reference: 'UNRA/SUPLS/23-24/004',
    subject: 'Software Licenses',
    status: 'Approved',
    dateCreated: 'Dec 18, 2023',
    dateRequired: 'Jan 5, 2024',
    budgetCode: 'BGT-2023-IT',
    budgetCategory: 'IT Infrastructure',
    description: 'Annual renewal of design software licenses',
    requestedBy: 'Michael Ssempala',
    contactPerson: 'Grace Atim, **********',
    department: 'IT',
    priority: 'High',
    deliveryInstructions: 'IT Department, 2nd Floor',
    isFavorite: false,
    lineItems: [
      {
        id: '01',
        description: 'Design Suite Pro License',
        partNumber: 'DSP-2023',
        unit: 'license',
        quantity: 5,
        hasAttachment: false,
        comments: 'Annual subscription'
      },
      {
        id: '02',
        description: 'Project Management Software',
        partNumber: 'PMS-2023',
        unit: 'license',
        quantity: 10,
        hasAttachment: false,
        comments: 'Cloud-based, annual subscription'
      }
    ]
  }
];

const statusVariantMap = {
  'Pending': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
  'In Review': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
  'Approved': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
  'Rejected': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
};

// Create a separate component that uses useSearchParams
function PendingRequisitionsContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const statusFilter = searchParams.get('status');
  
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' } | null>(null);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [activeFilter, setActiveFilter] = useState<string | null>(statusFilter || 'All');
  
  const handleSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };
  
  const sortedRequisitions = [...mockRequisitions].sort((a, b) => {
    if (!sortConfig) return 0;
    
    const aValue = a[sortConfig.key as keyof typeof a];
    const bValue = b[sortConfig.key as keyof typeof b];
    
    if (aValue < bValue) {
      return sortConfig.direction === 'asc' ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortConfig.direction === 'asc' ? 1 : -1;
    }
    return 0;
  });
  
  const filteredRequisitions = sortedRequisitions.filter(requisition => {
    const searchLower = searchTerm.toLowerCase();
    const matchesSearch = 
      requisition.id.toLowerCase().includes(searchLower) ||
      requisition.subject.toLowerCase().includes(searchLower) ||
      requisition.reference.toLowerCase().includes(searchLower);
      
    const matchesFilter = 
      activeFilter === 'All' || 
      requisition.status === activeFilter;
      
    return matchesSearch && matchesFilter;
  });
  
  return (
    <div className="p-6 bg-gray-50 dark:bg-slate-900 min-h-screen">
      {/* Breadcrumb */}
      <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-4">
        <svg className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M3 12h18M3 6h18M3 18h18" />
        </svg>
        <span className="mr-2">Pending requisitions</span>
      </div>

      {/* Page Header */}
      <div className="flex justify-between items-start mb-6">
        <div>
          <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            All relevant projects
          </h1>
        </div>
        <Button className="bg-blue-500 text-white hover:bg-blue-600 transition-colors duration-200 shadow hover:shadow-md text-sm rounded-md">
          <span className="mr-1">+</span> Create New
        </Button>
      </div>

      {/* Search Bar */}
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 p-3 mb-4">
        <div className="flex items-center justify-between">
          <div className="relative flex-1">
            <MagnifyingGlassIcon className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              placeholder="Search ID, Year, Subject, Reference..."
              className="pl-10 w-full border border-gray-200 h-9 text-sm"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex items-center space-x-2 ml-4">
            <Button variant="outline" className="flex items-center gap-1 h-9 text-sm border-gray-200">
              <FunnelIcon className="h-4 w-4" />
              <span>Filter</span>
            </Button>
            <Button variant="outline" className="flex items-center gap-1 h-9 text-sm border-gray-200">
              <span>Sort by</span>
              <ChevronDownIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Data Table */}
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50 dark:bg-slate-700 border-b border-gray-200">
                <TableHead className="font-medium text-xs text-gray-600 dark:text-gray-300 py-2 px-3">
                  <button 
                    className="flex items-center gap-1"
                    onClick={() => handleSort('id')}
                  >
                    Project ID
                    <ChevronDownIcon className="h-3 w-3 ml-1" />
                  </button>
                </TableHead>
                <TableHead className="font-medium text-xs text-gray-600 dark:text-gray-300 py-2 px-3">
                  <button 
                    className="flex items-center gap-1"
                    onClick={() => handleSort('year')}
                  >
                    Year
                    <ChevronDownIcon className="h-3 w-3 ml-1" />
                  </button>
                </TableHead>
                <TableHead className="font-medium text-xs text-gray-600 dark:text-gray-300 py-2 px-3">Case No.</TableHead>
                <TableHead className="font-medium text-xs text-gray-600 dark:text-gray-300 py-2 px-3">Reference</TableHead>
                <TableHead className="font-medium text-xs text-gray-600 dark:text-gray-300 py-2 px-3">
                  <button 
                    className="flex items-center gap-1"
                    onClick={() => handleSort('subject')}
                  >
                    Subject
                    <ChevronDownIcon className="h-3 w-3 ml-1" />
                  </button>
                </TableHead>
                <TableHead className="font-medium text-xs text-gray-600 dark:text-gray-300 py-2 px-3">
                  <button 
                    className="flex items-center gap-1"
                    onClick={() => handleSort('status')}
                  >
                    Status
                    <ChevronDownIcon className="h-3 w-3 ml-1" />
                  </button>
                </TableHead>
                <TableHead className="font-medium text-xs text-gray-600 dark:text-gray-300 py-2 px-3">Date created</TableHead>
                <TableHead className="font-medium text-xs text-gray-600 dark:text-gray-300 py-2 px-3">Budget code</TableHead>
                <TableHead className="font-medium text-xs text-gray-600 dark:text-gray-300 py-2 px-3">Budget category</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRequisitions.length > 0 ? (
                filteredRequisitions.map((requisition, index) => (
                  <TableRow 
                    key={requisition.id} 
                    className={`border-b border-gray-100 hover:bg-gray-50 dark:hover:bg-slate-700/50 cursor-pointer ${index % 2 === 0 ? 'bg-white dark:bg-slate-800' : 'bg-gray-50 dark:bg-slate-700/20'}`}
                    onClick={() => router.push(`/requisitions/${requisition.id}`)}
                  >
                    <TableCell className="py-2 px-3 text-sm">
                      {requisition.id}
                    </TableCell>
                    <TableCell className="py-2 px-3 text-sm">{requisition.year}</TableCell>
                    <TableCell className="py-2 px-3 text-sm">{requisition.caseNo}</TableCell>
                    <TableCell className="py-2 px-3 text-sm">{requisition.reference}</TableCell>
                    <TableCell className="py-2 px-3 text-sm">{requisition.subject}</TableCell>
                    <TableCell className="py-2 px-3 text-sm">
                      {requisition.status}
                    </TableCell>
                    <TableCell className="py-2 px-3 text-sm">{requisition.dateCreated}</TableCell>
                    <TableCell className="py-2 px-3 text-sm">{requisition.budgetCode}</TableCell>
                    <TableCell className="py-2 px-3 text-sm">{requisition.budgetCategory}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8 text-gray-500 dark:text-gray-400">
                    <div className="flex flex-col items-center justify-center space-y-2">
                      <DocumentTextIcon className="h-12 w-12 text-gray-300 dark:text-gray-600" />
                      <p>No pending requisitions found</p>
                      <p className="text-sm">Try adjusting your search or filter criteria</p>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        <div className="px-6 py-4 border-t border-gray-200 dark:border-slate-700 flex flex-col sm:flex-row items-center justify-between gap-4">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Showing <span className="font-medium">1</span> to{' '}
            <span className="font-medium">{Math.min(rowsPerPage, filteredRequisitions.length)}</span> of{' '}
            <span className="font-medium">{filteredRequisitions.length}</span> entries
          </div>
          
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500 dark:text-gray-400">Rows per page:</span>
            <select
              className="bg-white dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-md px-2 py-1 text-sm"
              value={rowsPerPage}
              onChange={(e) => setRowsPerPage(Number(e.target.value))}
            >
              {[10, 25, 50, 100].map((size) => (
                <option key={size} value={size}>
                  {size}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-center space-x-2">
            <button
              className="px-3 py-1 border border-gray-200 dark:border-slate-600 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={true}
            >
              Previous
            </button>
            <button
              className="px-3 py-1 border border-gray-200 dark:border-slate-600 rounded-md text-sm"
              disabled={true}
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}


export default function PendingRequisitionsPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    }>
      <PendingRequisitionsContent />
    </Suspense>
  );
}

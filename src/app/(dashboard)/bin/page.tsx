'use client';

import React, { useState } from 'react';
import { 
  FiTrash2, 
  FiSearch, 
  FiChevronDown, 
  FiChevronRight, 
  FiFile, 
  FiFolder, 
  FiArrowLeft,
  FiX,
  FiAlertTriangle,
  FiInbox
} from 'react-icons/fi';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

interface DeletedItem {
  id: string;
  name: string;
  type: 'file' | 'folder' | 'text';
  deletedAt: string;
  size?: string;
  deletedBy: string;
  extension?: string;
}

interface FilterState {
  types: string[];
  lastModified: string | null;
  fileSize: string | null;
}

interface DeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  itemName?: string;
  isPermanent?: boolean;
}

const DeleteDialog: React.FC<DeleteDialogProps> = ({ 
  isOpen, 
  onClose, 
  onConfirm, 
  itemName = 'selected items',
  isPermanent = false 
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {isPermanent ? 'Delete Permanently' : 'Delete Item'}
          </h3>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
          >
            <FiX className="h-5 w-5" />
          </button>
        </div>
        
        <div className="flex items-start">
          <div className="flex-shrink-0 h-10 w-10 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center text-red-600 dark:text-red-400">
            <FiAlertTriangle className="h-5 w-5" />
          </div>
          <div className="ml-3">
            <p className="text-gray-700 dark:text-gray-300">
              {isPermanent 
                ? `Are you sure you want to permanently delete ${itemName}? This action cannot be undone.`
                : `Move ${itemName} to the bin?`}
            </p>
          </div>
        </div>
        
        <div className="mt-6 flex justify-end space-x-3">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={() => {
              onConfirm();
              onClose();
            }}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            {isPermanent ? 'Delete Permanently' : 'Move to Bin'}
          </button>
        </div>
      </div>
    </div>
  );
};

const BinPage = () => {
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [showEmptyState, setShowEmptyState] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFilters, setExpandedFilters] = useState<string[]>(['type']);
  const [filters, setFilters] = useState<FilterState>({
    types: [],
    lastModified: null,
    fileSize: null
  });
  const [deleteDialog, setDeleteDialog] = useState<{
    isOpen: boolean;
    itemId: string | null;
    isPermanent: boolean;
  }>({
    isOpen: false,
    itemId: null,
    isPermanent: false
  });

  // Mock data for deleted items
  const deletedItems: DeletedItem[] = [
    {
      id: '1',
      name: 'Q1_Report_2025.pdf',
      type: 'file',
      deletedAt: '2 days ago',
      size: '2.4 MB',
      deletedBy: 'You',
      extension: 'pdf'
    },
    {
      id: '2',
      name: 'Project_Assets',
      type: 'folder',
      deletedAt: '1 week ago',
      deletedBy: 'John Doe'
    },
    {
      id: '3',
      name: 'Meeting_Notes_April.docx',
      type: 'file',
      deletedAt: '3 weeks ago',
      size: '1.1 MB',
      deletedBy: 'Jane Smith',
      extension: 'docx'
    },
    {
      id: '4',
      name: 'Budget_2025.xlsx',
      type: 'file',
      deletedAt: '1 month ago',
      size: '5.7 MB',
      deletedBy: 'You',
      extension: 'xlsx'
    },
    {
      id: '5',
      name: 'Design_Assets',
      type: 'folder',
      deletedAt: '2 months ago',
      deletedBy: 'Alex Johnson'
    },
    {
      id: '6',
      name: 'Presentation_Deck.pdf',
      type: 'file',
      deletedAt: '3 months ago',
      size: '8.2 MB',
      deletedBy: 'You',
      extension: 'pdf'
    },
    {
      id: '7',
      name: 'Project_Proposal.docx',
      type: 'file',
      deletedAt: '4 months ago',
      size: '1.5 MB',
      deletedBy: 'Sarah Williams',
      extension: 'docx'
    },
    {
      id: '8',
      name: 'Team_Photos',
      type: 'folder',
      deletedAt: '5 months ago',
      deletedBy: 'You'
    },
    {
      id: '9',
      name: 'Product_Requirements.pdf',
      type: 'file',
      deletedAt: '6 months ago',
      size: '3.2 MB',
      deletedBy: 'Michael Brown',
      extension: 'pdf'
    },
    {
      id: '10',
      name: 'User_Research',
      type: 'folder',
      deletedAt: '1 year ago',
      deletedBy: 'You'
    },
    {
      id: '11',
      name: 'README.txt',
      type: 'text',
      deletedAt: '1 day ago',
      size: '0.2 MB',
      deletedBy: 'You',
      extension: 'txt'
    },
    {
      id: '12',
      name: 'config.json',
      type: 'text',
      deletedAt: '5 days ago',
      size: '0.1 MB',
      deletedBy: 'John Doe',
      extension: 'json'
    },
    {
      id: '13',
      name: 'notes.md',
      type: 'text',
      deletedAt: '2 weeks ago',
      size: '0.3 MB',
      deletedBy: 'You',
      extension: 'md'
    }
  ];

  // Filter handler functions
  const toggleTypeFilter = (type: 'file' | 'folder' | 'text') => {
    setFilters(prev => ({
      ...prev,
      types: prev.types.includes(type)
        ? prev.types.filter(t => t !== type)
        : [...prev.types, type]
    }));
  };

  const setLastModifiedFilter = (value: string | null) => {
    setFilters(prev => ({
      ...prev,
      lastModified: prev.lastModified === value ? null : value
    }));
  };

  const setFileSizeFilter = (value: string | null) => {
    setFilters(prev => ({
      ...prev,
      fileSize: prev.fileSize === value ? null : value
    }));
  };

  const clearAllFilters = () => {
    setFilters({
      types: [],
      lastModified: null,
      fileSize: null
    });
    setSearchQuery('');
  };

  // Check if any filters are active
  const hasActiveFilters = 
    filters.types.length > 0 || 
    filters.lastModified !== null || 
    filters.fileSize !== null ||
    searchQuery !== '';

  // Filter logic
  const filteredItems = deletedItems.filter(item => {
    // Search filter
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase());
    
    // Type filter
    const matchesType = filters.types.length === 0 || filters.types.includes(item.type);
    
    // Last modified filter (simplified for demo)
    let matchesLastModified = true;
    if (filters.lastModified === '7days') {
      matchesLastModified = item.deletedAt.includes('day') || item.deletedAt.includes('week');
    } else if (filters.lastModified === '30days') {
      matchesLastModified = !item.deletedAt.includes('year');
    } else if (filters.lastModified === 'older') {
      matchesLastModified = item.deletedAt.includes('month') || item.deletedAt.includes('year');
    }
    
    // File size filter (simplified for demo)
    let matchesFileSize = true;
    if (filters.fileSize === 'small' && item.size) {
      const sizeInMB = parseFloat(item.size);
      matchesFileSize = sizeInMB < 10;
    } else if (filters.fileSize === 'medium' && item.size) {
      const sizeInMB = parseFloat(item.size);
      matchesFileSize = sizeInMB >= 10 && sizeInMB < 100;
    } else if (filters.fileSize === 'large' && item.size) {
      const sizeInMB = parseFloat(item.size);
      matchesFileSize = sizeInMB >= 100;
    } else if (filters.fileSize === 'large' && !item.size) {
      matchesFileSize = false; // Folders don't have size
    }
    
    return matchesSearch && matchesType && matchesLastModified && matchesFileSize;
  });



  const toggleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      setSelectedItems(deletedItems.map(item => item.id));
    } else {
      setSelectedItems([]);
    }
  };

  const toggleSelectItem = (id: string) => {
    setSelectedItems(prev => 
      prev.includes(id) 
        ? prev.filter(itemId => itemId !== id)
        : [...prev, id]
    );
  };

  const restoreItems = () => {
    // Implement restore logic here
    setSelectedItems([]);
  };

  const deletePermanently = (id?: string) => {
    const idsToDelete = id ? [id] : selectedItems;
    
    // In a real app, you would make an API call here
    console.log('Permanently deleting items:', idsToDelete);
    
    // For demo purposes, we'll just show a toast
    toast.success(
      idsToDelete.length > 1 
        ? `${idsToDelete.length} items permanently deleted` 
        : 'Item permanently deleted',
      { position: 'bottom-right' }
    );
    
    // Clear selection and close dialog
    setSelectedItems(prev => prev.filter(id => !idsToDelete.includes(id)));
    setDeleteDialog({ isOpen: false, itemId: null, isPermanent: false });
  };
  
  const moveToBin = (id: string) => {
    // In a real app, you would make an API call here
    console.log('Moving to bin:', id);
    
    // For demo purposes, we'll just show a toast
    toast.success('Item moved to bin', { position: 'bottom-right' });
    
    // Close dialog
    setDeleteDialog({ isOpen: false, itemId: null, isPermanent: false });
  };

  const emptyBin = () => {
    if (window.confirm('Are you sure you want to empty the bin? This action cannot be undone.')) {
      // In a real app, you would make an API call here
      console.log('Emptying bin');
      
      // For demo purposes, we'll just show a toast and reset the state
      toast.success('Bin emptied successfully', { position: 'bottom-right' });
      setShowEmptyState(true);
    }
  };

  const toggleFilter = (filter: string) => {
    setExpandedFilters(prev =>
      prev.includes(filter)
        ? prev.filter(f => f !== filter)
        : [...prev, filter]
    );
  };

  if (showEmptyState) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="text-center py-12">
          <div className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
            <FiTrash2 className="w-8 h-8 text-gray-400" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Your bin is empty</h2>
          <p className="text-gray-500 dark:text-gray-400 mb-6">
            Items in the bin will be automatically deleted after 30 days.
          </p>
          <button
            onClick={() => setShowEmptyState(false)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  const handleDeleteClick = (id: string, isPermanent: boolean = false) => {
    setDeleteDialog({
      isOpen: true,
      itemId: id,
      isPermanent
    });
  };

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6 relative">
      <DeleteDialog
        isOpen={deleteDialog.isOpen}
        onClose={() => setDeleteDialog(prev => ({ ...prev, isOpen: false }))}
        onConfirm={() => {
          if (deleteDialog.isPermanent) {
            deletePermanently(deleteDialog.itemId || undefined);
          } else if (deleteDialog.itemId) {
            moveToBin(deleteDialog.itemId);
          }
        }}
        itemName={
          deleteDialog.itemId 
            ? `"${deletedItems.find(item => item.id === deleteDialog.itemId)?.name || 'this item'}"` 
            : `${selectedItems.length} selected items`
        }
        isPermanent={deleteDialog.isPermanent}
      />
      
      {/* Toast container */}
      <div className="fixed bottom-4 right-4 z-50">
        {toast.isActive('empty-bin') && (
          <div className="bg-red-600 text-white px-4 py-2 rounded-md shadow-lg flex items-center">
            <FiAlertTriangle className="mr-2" />
            <span>Bin emptied successfully</span>
          </div>
        )}
      </div>
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center bg-gradient-to-r from-primary-50 to-primary-50/30 dark:from-gray-800 dark:to-gray-800/80 p-6 rounded-xl border border-primary-100 dark:border-gray-700">
        <div className="space-y-1">
          <div className="flex items-center">
            <div className="p-2 rounded-lg bg-primary-100 dark:bg-primary-900/30 mr-3">
              <FiTrash2 className="w-5 h-5 text-primary-600 dark:text-primary-400" />
            </div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-primary-600 to-primary-800 dark:from-primary-400 dark:to-primary-200 bg-clip-text text-transparent">
              Bin
            </h1>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-300 ml-11">
            Items in the bin will be automatically deleted after 30 days.
          </p>
        </div>
        <div className="flex gap-3 w-full sm:w-auto mt-4 sm:mt-0">
          <button
            onClick={emptyBin}
            className="group relative inline-flex items-center px-4 py-2.5 border border-red-200 dark:border-red-900/50 text-sm font-medium rounded-lg text-red-700 dark:text-red-300 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200 hover:shadow-sm"
          >
            <span className="absolute inset-0 bg-red-500/10 dark:bg-red-500/5 group-hover:bg-red-500/20 dark:group-hover:bg-red-500/10 rounded-lg transition-all duration-200 opacity-0 group-hover:opacity-100"></span>
            <span className="relative flex items-center">
              <FiTrash2 className="mr-2 w-4 h-4" />
              Empty Bin
            </span>
          </button>
        </div>
      </div>

      <div className="flex flex-col lg:flex-row gap-6">
        {/* Filters */}
        <div className="w-full lg:w-72 flex-shrink-0 space-y-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700/50 p-5 hover:shadow-md transition-shadow duration-200">
            <div>
              <div className="flex items-center justify-between mb-1">
                <label htmlFor="search" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Search</label>
                {hasActiveFilters && (
                  <button 
                    onClick={clearAllFilters}
                    className="text-xs text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-300"
                  >
                    Clear all
                  </button>
                )}
              </div>
              <div className="relative rounded-lg shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiSearch className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  id="search"
                  type="text"
                  placeholder="Search in bin..."
                  className="block w-full pl-10 pr-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg leading-5 bg-white dark:bg-gray-900/50 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition duration-150 ease-in-out sm:text-sm"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            <div className="mt-6 space-y-6">
              <div>
                <button
                  onClick={() => toggleFilter('type')}
                  className="flex items-center justify-between w-full text-left text-sm font-medium text-gray-700 dark:text-gray-200 group"
                >
                  <span className="font-semibold">File Type</span>
                  {expandedFilters.includes('type') ? (
                    <FiChevronDown className="h-4 w-4 text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-300 transition-colors" />
                  ) : (
                    <FiChevronRight className="h-4 w-4 text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-300 transition-colors" />
                  )}
                </button>
                {expandedFilters.includes('type') && (
                  <div className="mt-3 space-y-2 pl-0.5">
                    <label className="flex items-center group cursor-pointer">
                      <div className="relative flex items-center">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={filters.types.includes('file')}
                          onChange={() => toggleTypeFilter('file')}
                        />
                        <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                      </div>
                      <span className="ml-3 text-sm text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white transition-colors">Files</span>
                    </label>
                    <label className="flex items-center group cursor-pointer">
                      <div className="relative flex items-center">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={filters.types.includes('folder')}
                          onChange={() => toggleTypeFilter('folder')}
                        />
                        <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                      </div>
                      <span className="ml-3 text-sm text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white transition-colors">Folders</span>
                    </label>
                    <label className="flex items-center group cursor-pointer">
                      <div className="relative flex items-center">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={filters.types.includes('text')}
                          onChange={() => toggleTypeFilter('text')}
                        />
                        <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                      </div>
                      <span className="ml-3 text-sm text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white transition-colors">Text Files</span>
                    </label>
                  </div>
                )}
              </div>
              
              <div className="pt-4 border-t border-gray-100 dark:border-gray-700">
                <h3 className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">Last Modified</h3>
                <div className="space-y-2">
                  <button 
                    onClick={() => setLastModifiedFilter('7days')}
                    className={`block w-full text-left px-3 py-2 text-sm rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 ${filters.lastModified === '7days' ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300' : 'text-gray-700 dark:text-gray-300'}`}
                  >
                    Last 7 days
                  </button>
                  <button 
                    onClick={() => setLastModifiedFilter('30days')}
                    className={`block w-full text-left px-3 py-2 text-sm rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 ${filters.lastModified === '30days' ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300' : 'text-gray-700 dark:text-gray-300'}`}
                  >
                    Last 30 days
                  </button>
                  <button 
                    onClick={() => setLastModifiedFilter('older')}
                    className={`block w-full text-left px-3 py-2 text-sm rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 ${filters.lastModified === 'older' ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300' : 'text-gray-700 dark:text-gray-300'}`}
                  >
                    Older
                  </button>
                </div>
              </div>

              <div className="pt-4 border-t border-gray-100 dark:border-gray-700">
                <h3 className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">File Size</h3>
                <div className="space-y-2">
                  <button 
                    onClick={() => setFileSizeFilter('small')}
                    className={`block w-full text-left px-3 py-2 text-sm rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 ${filters.fileSize === 'small' ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300' : 'text-gray-700 dark:text-gray-300'}`}
                  >
                    Small (0-10 MB)
                  </button>
                  <button 
                    onClick={() => setFileSizeFilter('medium')}
                    className={`block w-full text-left px-3 py-2 text-sm rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 ${filters.fileSize === 'medium' ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300' : 'text-gray-700 dark:text-gray-300'}`}
                  >
                    Medium (10-100 MB)
                  </button>
                  <button 
                    onClick={() => setFileSizeFilter('large')}
                    className={`block w-full text-left px-3 py-2 text-sm rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 ${filters.fileSize === 'large' ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300' : 'text-gray-700 dark:text-gray-300'}`}
                  >
                    Large (100+ MB)
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="flex-1">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700/50 overflow-hidden">
            {/* Bulk actions */}
            {selectedItems.length > 0 && (
              <div className="px-4 py-3 border-b border-gray-100 dark:border-gray-700/50 bg-gray-50 dark:bg-gray-800/50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      {selectedItems.length} {selectedItems.length === 1 ? 'item' : 'items'} selected
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={restoreItems}
                      className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 dark:bg-primary-900/30 dark:text-primary-300 dark:hover:bg-primary-900/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
                    >
                      Restore
                    </button>
                    <button
                      onClick={() => {
                        if (window.confirm(`Are you sure you want to permanently delete ${selectedItems.length} items? This action cannot be undone.`)) {
                          deletePermanently();
                        }
                      }}
                      className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-300 dark:hover:bg-red-900/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                      Delete permanently
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Items list */}
            <div className="divide-y divide-gray-100 dark:divide-gray-700/50">
              {filteredItems.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700/50">
                    <thead className="bg-gray-50 dark:bg-gray-800/50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          <input
                            type="checkbox"
                            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            checked={selectedItems.length === filteredItems.length && filteredItems.length > 0}
                            onChange={toggleSelectAll}
                            disabled={filteredItems.length === 0}
                          />
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Name
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Deleted
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Deleted by
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Size
                        </th>
                        <th scope="col" className="relative px-6 py-3">
                          <span className="sr-only">Actions</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700/50">
                      {filteredItems.map((item, index) => (
                        <tr 
                          key={item.id}
                          className={`${index % 2 === 0 ? 'bg-white dark:bg-gray-800' : 'bg-gray-50 dark:bg-gray-700/30'} hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors`}
                        >
                          <td className="px-6 py-4 whitespace-nowrap">
                            <input
                              type="checkbox"
                              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                              checked={selectedItems.includes(item.id)}
                              onChange={() => toggleSelectItem(item.id)}
                            />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className={`flex-shrink-0 h-10 w-10 rounded-lg flex items-center justify-center ${
                                item.type === 'text' 
                                  ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400' 
                                  : item.type === 'file' 
                                    ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' 
                                    : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400'
                              }`}>
                                {item.type === 'file' ? (
                                  <FiFile className="h-5 w-5" />
                                ) : item.type === 'text' ? (
                                  <span className="text-xs font-medium">{item.extension?.toUpperCase()}</span>
                                ) : (
                                  <FiFolder className="h-5 w-5" />
                                )}
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900 dark:text-white">
                                  {item.name}
                                </div>
                                <div className="text-xs text-gray-500 dark:text-gray-400">
                                  {item.type === 'file' ? 'File' : 'Folder'}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900 dark:text-white">
                              {item.deletedAt}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-sm font-medium text-gray-700 dark:text-gray-300">
                                {item.deletedBy.split(' ').map(n => n[0]).join('').toUpperCase()}
                              </div>
                              <div className="ml-3">
                                <div className="text-sm font-medium text-gray-900 dark:text-white">
                                  {item.deletedBy}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {item.size || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              onClick={() => {
                                // In a real app, you would make an API call here
                                console.log('Restoring item:', item.id);
                                toast.success(`"${item.name}" restored`, { position: 'bottom-right' });
                                setSelectedItems(prev => prev.filter(id => id !== item.id));
                              }}
                              className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300"
                            >
                              Restore
                            </button>
                            <span className="text-gray-300 dark:text-gray-600">|</span>
                            <button
                              onClick={() => handleDeleteClick(item.id, true)}
                              className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                            >
                              Delete
                            </button>
                          </td>
                        </tr>
                      ))}
                    {filteredItems.length === 0 && (
                    <tr>
                      <td colSpan={5} className="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
                        <div className="flex flex-col items-center justify-center space-y-2">
                          <FiInbox className="h-12 w-12 text-gray-300 dark:text-gray-600" />
                          <p className="text-sm">No items found matching your criteria</p>
                        </div>
                      </td>
                    </tr>
                  )}
                  </tbody>
                  </table>
                </div>
              ) : (
                <div className="px-6 py-12 text-center">
                  <FiSearch className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                    {hasActiveFilters ? 'No matching items found' : 'No items in bin'}
                  </h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    {hasActiveFilters 
                      ? 'Try adjusting your search or filter criteria'
                      : 'Items you delete will appear here.'}
                  </p>
                  {hasActiveFilters && (
                    <button
                      onClick={clearAllFilters}
                      className="mt-4 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 dark:bg-primary-900/30 dark:text-primary-300 dark:hover:bg-primary-900/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Clear all filters
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BinPage;

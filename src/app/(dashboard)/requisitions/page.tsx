'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/Card';

export default function RequisitionsPage() {
  const router = useRouter();
  
  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">Requisitions</h1>
        <p className="text-gray-600">View and manage all requisitions</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="bg-white dark:bg-slate-800 shadow-sm">
          <CardContent className="p-6">
            <h2 className="text-lg font-semibold mb-4">Pending Requisitions</h2>
            <p className="text-gray-600 mb-4">View and manage requisitions that are pending approval or processing.</p>
            <Button 
              onClick={() => router.push('/pending-requisitions')}
              className="bg-[#18546c] hover:bg-[#1a6985]"
            >
              View Pending Requisitions
            </Button>
          </CardContent>
        </Card>
        
        <Card className="bg-white dark:bg-slate-800 shadow-sm">
          <CardContent className="p-6">
            <h2 className="text-lg font-semibold mb-4">Create New Requisition</h2>
            <p className="text-gray-600 mb-4">Create a new requisition for goods or services.</p>
            <Button 
              onClick={() => router.push('/requisitions/new')}
              className="bg-[#18546c] hover:bg-[#1a6985]"
            >
              Create Requisition
            </Button>
          </CardContent>
        </Card>
        
        <Card className="bg-white dark:bg-slate-800 shadow-sm">
          <CardContent className="p-6">
            <h2 className="text-lg font-semibold mb-4">Requisition Reports</h2>
            <p className="text-gray-600 mb-4">Generate and view reports for requisitions.</p>
            <Button 
              onClick={() => router.push('/reports?type=requisitions')}
              className="bg-[#18546c] hover:bg-[#1a6985]"
            >
              View Reports
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

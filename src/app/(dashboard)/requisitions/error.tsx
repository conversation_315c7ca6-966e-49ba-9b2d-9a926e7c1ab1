'use client';

import React from 'react';
import { Button } from '@/components/ui/button';

export default function RequisitionsError({
  error,
  reset,
}: {
  error: Error;
  reset: () => void;
}) {
  return (
    <div className="flex flex-col items-center justify-center p-8 min-h-[60vh]">
      <div className="bg-white dark:bg-slate-800 p-8 rounded-lg shadow-md max-w-md w-full text-center">
        <h2 className="text-2xl font-bold text-red-600 dark:text-red-400 mb-4">
          Error Loading Requisitions
        </h2>
        <p className="text-gray-700 dark:text-gray-300 mb-6">
          {error.message || 'An unexpected error occurred while loading requisitions.'}
        </p>
        <div className="flex justify-center gap-4">
          <Button
            onClick={() => reset()}
            className="bg-[#18546c] hover:bg-[#1a6985]"
          >
            Try Again
          </Button>
          <Button
            onClick={() => window.location.href = '/'}
            variant="outline"
          >
            Go to Dashboard
          </Button>
        </div>
      </div>
    </div>
  );
}

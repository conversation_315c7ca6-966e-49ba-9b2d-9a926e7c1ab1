'use client';

import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';

export default function RequisitionNotFound() {
  return (
    <div className="flex flex-col items-center justify-center p-8 min-h-[60vh]">
      <div className="bg-white dark:bg-slate-800 p-8 rounded-lg shadow-md max-w-md w-full text-center">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-4">
          Requisition Not Found
        </h2>
        <p className="text-gray-700 dark:text-gray-300 mb-6">
          The requisition you are looking for does not exist or has been removed.
        </p>
        <div className="flex justify-center gap-4">
          <Link href="/requisitions">
            <Button className="bg-[#18546c] hover:bg-[#1a6985]">
              Back to Requisitions
            </Button>
          </Link>
          <Link href="/pending-requisitions">
            <Button variant="outline">
              View Pending Requisitions
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}

'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { StarIcon } from '@heroicons/react/24/solid';
import { StarIcon as StarOutlineIcon } from '@heroicons/react/24/outline';
import { ChevronLeftIcon, ChevronDownIcon, MagnifyingGlassIcon, FunnelIcon, PaperClipIcon, ChatBubbleLeftIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/Card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

// Mock data for the table
const mockRequisitions = [
  {
    id: '0002',
    year: '2024',
    caseNo: '173',
    reference: 'UNRA/SUPLS/24-25/173',
    subject: 'Spare parts vehicle UAO 257R',
    status: 'Requisition',
    dateCreated: 'Oct 14, 2024',
    dateRequired: 'Sep 18, 2024',
    budgetCode: '228002',
    budgetCategory: 'Maintenance vehicles',
    description: 'Spare parts for vehicle UAO 257R',
    requestedBy: 'Brian Gimel',
    contactPerson: 'Anne Kalungi, 0777074589',
    department: 'Mechanical',
    priority: 'Urgent',
    deliveryInstructions: 'Toyota Arua, 15 Arua Road',
    isFavorite: true,
    lineItems: [
      {
        id: '01',
        description: 'Diesel Filter FF5880',
        partNumber: 'FF5880',
        unit: 'pcs',
        quantity: 2,
        hasAttachment: true,
        comments: 'For Nissan Patrol'
      },
      {
        id: '02',
        description: 'Oil Filter LF16352',
        partNumber: 'LF16352',
        unit: 'pcs',
        quantity: 1,
        hasAttachment: true,
        comments: 'For Nissan Patrol'
      },
      {
        id: '03',
        description: 'Air Filter AF26124',
        partNumber: 'AF26124',
        unit: 'pcs',
        quantity: 1,
        hasAttachment: false,
        comments: 'For Nissan Patrol'
      },
      {
        id: '04',
        description: 'Fuel Filter FS19532',
        partNumber: 'FS19532',
        unit: 'pcs',
        quantity: 1,
        hasAttachment: false,
        comments: 'For Nissan Patrol'
      },
      {
        id: '05',
        description: 'Brake Pads BP4565',
        partNumber: 'BP4565',
        unit: 'set',
        quantity: 2,
        hasAttachment: true,
        comments: 'Front and rear'
      },
      {
        id: '06',
        description: 'Chassis Repair Kit',
        partNumber: 'QP0550',
        unit: 'pcs',
        quantity: 6,
        hasAttachment: false,
        comments: 'Chasis No. X34GK-RK047FU-BID'
      }
    ]
  },
  {
    id: '0003',
    year: '2024',
    caseNo: '179',
    reference: 'UNRA/SUPLS/24-25/179',
    subject: 'Lawn mower',
    status: 'Requisition',
    dateCreated: 'Oct 12, 2024',
    dateRequired: 'Oct 30, 2024',
    budgetCode: '228003',
    budgetCategory: 'Maintenance machinery',
    description: 'Industrial lawn mower for headquarters compound',
    requestedBy: 'Sarah Johnson',
    contactPerson: 'Mark Peters, 0788123456',
    department: 'Facilities',
    priority: 'Normal',
    deliveryInstructions: 'Main Warehouse, Plot 45 Industrial Area',
    isFavorite: false,
    lineItems: [
      {
        id: '01',
        description: 'Industrial Lawn Mower 5HP',
        partNumber: 'LM5000',
        unit: 'unit',
        quantity: 1,
        hasAttachment: true,
        comments: 'With spare blades'
      }
    ]
  },
  {
    id: '0005',
    year: '2024',
    caseNo: '178',
    reference: 'UNRA/SUPLS/24-25/178',
    subject: 'Water pump spares',
    status: 'Requisition',
    dateCreated: 'Oct 11, 2024',
    dateRequired: 'Nov 5, 2024',
    budgetCode: '228004',
    budgetCategory: 'Construction equipment',
    description: 'Replacement parts for water pump at northern facility',
    requestedBy: 'David Mukasa',
    contactPerson: 'Robert Kigozi, 0756789012',
    department: 'Engineering',
    priority: 'High',
    deliveryInstructions: 'Northern Facility, Plot 12 Gulu Road',
    isFavorite: false,
    lineItems: [
      {
        id: '01',
        description: 'Water Pump Impeller',
        partNumber: 'WPI-450',
        unit: 'pcs',
        quantity: 2,
        hasAttachment: false,
        comments: 'For model WP-2000'
      },
      {
        id: '02',
        description: 'Pump Shaft Seal',
        partNumber: 'PSS-120',
        unit: 'pcs',
        quantity: 5,
        hasAttachment: true,
        comments: 'Silicon type'
      }
    ]
  },
  {
    id: '0006',
    year: '2024',
    caseNo: '177',
    reference: 'UNRA/SUPLS/24-25/177',
    subject: 'Carburetor assembly',
    status: 'Requisition',
    dateCreated: 'Oct 13, 2024',
    dateRequired: 'Oct 25, 2024',
    budgetCode: '228005',
    budgetCategory: 'Maintenance machinery',
    description: 'Replacement carburetor for generator set',
    requestedBy: 'Patricia Nambi',
    contactPerson: 'James Ochieng, 0701234567',
    department: 'Operations',
    priority: 'Normal',
    deliveryInstructions: 'Main Workshop, Industrial Area',
    isFavorite: true,
    lineItems: [
      {
        id: '01',
        description: 'Carburetor Assembly Complete',
        partNumber: 'CA-G500',
        unit: 'set',
        quantity: 1,
        hasAttachment: true,
        comments: 'For Generator Model G-5000'
      },
      {
        id: '02',
        description: 'Fuel Line',
        partNumber: 'FL-250',
        unit: 'meters',
        quantity: 3,
        hasAttachment: false,
        comments: '8mm diameter'
      }
    ]
  },
  {
    id: '0007',
    year: '2023',
    caseNo: 'C-003',
    reference: 'UNRA/SUPLS/23-24/003',
    subject: 'Office Furniture',
    status: 'Pending',
    dateCreated: 'Dec 17, 2023',
    dateRequired: 'Jan 15, 2024',
    budgetCode: 'BGT-2023-OFF',
    budgetCategory: 'Office Expenses',
    description: 'New furniture for conference room',
    requestedBy: 'Elizabeth Nantume',
    contactPerson: 'Peter Okello, 0772345678',
    department: 'Administration',
    priority: 'Low',
    deliveryInstructions: 'Head Office, 3rd Floor',
    isFavorite: false,
    lineItems: [
      {
        id: '01',
        description: 'Conference Table',
        partNumber: 'CT-12',
        unit: 'pcs',
        quantity: 1,
        hasAttachment: true,
        comments: '12-seater, oak finish'
      },
      {
        id: '02',
        description: 'Executive Chairs',
        partNumber: 'EC-01',
        unit: 'pcs',
        quantity: 12,
        hasAttachment: true,
        comments: 'Black leather, adjustable height'
      }
    ]
  },
  {
    id: '0008',
    year: '2023',
    caseNo: 'C-004',
    reference: 'UNRA/SUPLS/23-24/004',
    subject: 'Software Licenses',
    status: 'Approved',
    dateCreated: 'Dec 18, 2023',
    dateRequired: 'Jan 5, 2024',
    budgetCode: 'BGT-2023-IT',
    budgetCategory: 'IT Infrastructure',
    description: 'Annual renewal of design software licenses',
    requestedBy: 'Michael Ssempala',
    contactPerson: 'Grace Atim, **********',
    department: 'IT',
    priority: 'High',
    deliveryInstructions: 'IT Department, 2nd Floor',
    isFavorite: false,
    lineItems: [
      {
        id: '01',
        description: 'Design Suite Pro License',
        partNumber: 'DSP-2023',
        unit: 'license',
        quantity: 5,
        hasAttachment: false,
        comments: 'Annual subscription'
      },
      {
        id: '02',
        description: 'Project Management Software',
        partNumber: 'PMS-2023',
        unit: 'license',
        quantity: 10,
        hasAttachment: false,
        comments: 'Cloud-based, annual subscription'
      }
    ]
  }
];

export default function RequisitionDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [requisition, setRequisition] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [isFavorite, setIsFavorite] = useState(false);

  useEffect(() => {
    // Simulate API call to fetch requisition details
    const id = params.id as string;
    const foundRequisition = mockRequisitions.find(req => req.id === id);
    
    if (foundRequisition) {
      setRequisition(foundRequisition);
      setIsFavorite(foundRequisition.isFavorite || false);
    }
    
    setLoading(false);
  }, [params.id]);

  const toggleFavorite = () => {
    setIsFavorite(!isFavorite);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full p-6">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading requisition details...</p>
        </div>
      </div>
    );
  }

  if (!requisition) {
    return (
      <div className="p-6">
        <div className="max-w-4xl mx-auto">
          <div className="mb-6">
            <Button 
              variant="outline" 
              onClick={() => router.back()}
              className="flex items-center gap-2"
            >
              <ChevronLeftIcon className="h-4 w-4" />
              Back to requisitions
            </Button>
          </div>
          
          <Card className="bg-white dark:bg-slate-800 shadow-sm">
            <CardContent className="p-8 text-center">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
                Requisition Not Found
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                The requisition you are looking for does not exist or has been removed.
              </p>
              <Link href="/pending-requisitions">
                <Button>Return to Requisitions</Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-[#f8f9fa]">
      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Link href="/" className="text-sm text-gray-600">{requisition.caseNo} {requisition.department}</Link>
            <span className="text-gray-400">/</span>
            <span className="text-sm text-gray-600">{requisition.reference}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" className="text-xs bg-white border-gray-300 text-gray-700">
              Delete Case
            </Button>
            <Button size="sm" className="text-xs bg-[#18546c] hover:bg-[#1a6985]">
              Save
            </Button>
            <Button size="sm" className="text-xs bg-[#18546c] hover:bg-[#1a6985]">
              Save and Submit
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-4 py-6">
        {/* Title Section */}
        <div className="mb-6">
          <div className="flex items-center">
            <h1 className="text-2xl font-bold text-gray-900">{requisition.reference}</h1>
            <button 
              onClick={toggleFavorite}
              className="ml-2 text-yellow-500 focus:outline-none"
            >
              {isFavorite ? 
                <StarIcon className="h-6 w-6" /> : 
                <StarOutlineIcon className="h-6 w-6" />
              }
            </button>
          </div>
          <p className="text-gray-600">{requisition.subject}</p>
        </div>

        {/* Status Tabs */}
        <div className="mb-8">
          <div className="flex border-b border-gray-200">
            <div className="flex space-x-1">
              <div className="flex items-center justify-center px-4 py-2 border-b-2 border-[#18546c] text-[#18546c] font-medium">
                <div className="w-6 h-6 rounded-full bg-[#18546c] flex items-center justify-center text-white mr-2">1</div>
                Requisition
              </div>
              <div className="flex items-center justify-center px-4 py-2 text-gray-500">
                <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 mr-2">2</div>
                Inquiry Sent
              </div>
              <div className="flex items-center justify-center px-4 py-2 text-gray-500">
                <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 mr-2">3</div>
                Approval
              </div>
              <div className="flex items-center justify-center px-4 py-2 text-gray-500">
                <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 mr-2">4</div>
                Purchase Order
              </div>
              <div className="flex items-center justify-center px-4 py-2 text-gray-500">
                <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 mr-2">5</div>
                Goods Received
              </div>
              <div className="flex items-center justify-center px-4 py-2 text-gray-500">
                <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 mr-2">6</div>
                Invoice
              </div>
            </div>
          </div>
        </div>

        {/* Information Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card className="bg-white dark:bg-slate-800 shadow-sm">
            <CardContent className="p-4">
              <div className="space-y-2">
                <div>
                  <p className="text-sm font-medium text-gray-500">Case Number</p>
                  <p className="text-sm font-medium">{requisition.caseNo}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Date Created</p>
                  <p className="text-sm font-medium">{requisition.dateCreated}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Date Required</p>
                  <p className="text-sm font-medium">{requisition.dateRequired}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-white dark:bg-slate-800 shadow-sm">
            <CardContent className="p-4">
              <div className="space-y-2">
                <div>
                  <p className="text-sm font-medium text-gray-500">Priority</p>
                  <Badge className={`
                    ${requisition.priority === 'Urgent' ? 'bg-red-100 text-red-800' : 
                      requisition.priority === 'High' ? 'bg-orange-100 text-orange-800' : 
                      'bg-green-100 text-green-800'}
                  `}>
                    {requisition.priority}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Budget Code</p>
                  <p className="text-sm font-medium">{requisition.budgetCode}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Budget Category</p>
                  <p className="text-sm font-medium">{requisition.budgetCategory}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-white dark:bg-slate-800 shadow-sm">
            <CardContent className="p-4">
              <div className="space-y-2">
                <div>
                  <p className="text-sm font-medium text-gray-500">Requested By</p>
                  <p className="text-sm font-medium">{requisition.requestedBy}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Department</p>
                  <p className="text-sm font-medium">{requisition.department}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Contact Person</p>
                  <p className="text-sm font-medium">{requisition.contactPerson}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <Card className="bg-white dark:bg-slate-800 shadow-sm mb-6">
          <CardContent className="p-4">
            <div className="space-y-2">
              <div>
                <p className="text-sm font-medium text-gray-500">Description</p>
                <p className="text-sm font-medium">{requisition.description}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Delivery Instructions</p>
                <p className="text-sm font-medium">{requisition.deliveryInstructions}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Line Items Section */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-4">Line Items</h2>
          
          {/* Search and Filter */}
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <div className="relative flex-1">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input 
                placeholder="Search line items..." 
                className="pl-9 bg-white dark:bg-slate-800"
              />
            </div>
            <div className="flex gap-2">
              <Button variant="outline" className="flex items-center gap-1">
                <FunnelIcon className="h-4 w-4" />
                Filter
                <ChevronDownIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          {/* Line Items Table */}
          <div className="border rounded-md overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50 dark:bg-slate-800">
                  <TableHead className="font-medium">Description</TableHead>
                  <TableHead className="font-medium">Part Number</TableHead>
                  <TableHead className="font-medium text-center">Unit</TableHead>
                  <TableHead className="font-medium text-center">Quantity</TableHead>
                  <TableHead className="font-medium text-center">Attachments</TableHead>
                  <TableHead className="font-medium text-center">Comments</TableHead>
                  <TableHead className="font-medium text-center">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {requisition.lineItems.map((item: any, index: number) => (
                  <TableRow key={item.id} className={index % 2 === 0 ? 'bg-white dark:bg-slate-900' : 'bg-gray-50 dark:bg-slate-800'}>
                    <TableCell className="font-medium">{item.description}</TableCell>
                    <TableCell>{item.partNumber}</TableCell>
                    <TableCell className="text-center">{item.unit}</TableCell>
                    <TableCell className="text-center">{item.quantity}</TableCell>
                    <TableCell className="text-center">
                      {item.hasAttachment ? (
                        <button className="text-blue-600 hover:text-blue-800">
                          <PaperClipIcon className="h-5 w-5 mx-auto" />
                        </button>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                    <TableCell className="text-center">
                      {item.comments ? (
                        <button className="text-gray-600 hover:text-gray-800" title={item.comments}>
                          <ChatBubbleLeftIcon className="h-5 w-5 mx-auto" />
                        </button>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex justify-center space-x-2">
                        <button className="text-gray-600 hover:text-blue-600">
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button className="text-gray-600 hover:text-red-600">
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>
    </div>
  );
}

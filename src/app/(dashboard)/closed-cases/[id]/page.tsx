"use client";

import React from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { FiArrowLeft, FiClock, FiCheckCircle, FiAlertCircle, FiPrinter, FiShare2, FiDownload, FiEdit2, FiMessageSquare, FiFileText, FiCalendar, FiUser, FiTag, FiAlertTriangle, FiCheck, FiX, FiChevronRight, FiMail, FiPhone, FiRefreshCw } from 'react-icons/fi';
import { Button } from '@/components/ui/button';
// Using a simple badge component since the UI library badge is not available
const Badge = ({ children, className = '', variant = 'default', ...props }: { children: React.ReactNode, className?: string, variant?: 'default' | 'outline' }) => (
  <span 
    className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${className}`}
    {...props}
  >
    {children}
  </span>
);
// Simple Tabs implementation
const Tabs = ({ 
  children, 
  value, 
  onValueChange, 
  className = '' 
}: { 
  children: React.ReactNode, 
  value: string, 
  onValueChange: (value: string) => void, 
  className?: string 
}) => {
  const [activeTab, setActiveTab] = React.useState(value);

  const handleTabChange = (newValue: string) => {
    setActiveTab(newValue);
    onValueChange(newValue);
  };

  const childrenWithProps = React.Children.map(children, (child) => {
    if (React.isValidElement<{ activeTab?: string; onTabChange?: (value: string) => void }>(child)) {
      if (child.type === TabsList) {
        return React.cloneElement(child, { 
          activeTab, 
          onTabChange: handleTabChange 
        });
      } else if (child.type === TabsContent) {
        return React.cloneElement(child, { 
          activeTab 
        });
      }
    }
    return child;
  });

  return <div className={className}>{childrenWithProps}</div>;
};

interface TabTriggerProps {
  value: string;
  children: React.ReactNode;
  isActive?: boolean;
  onClick?: () => void;
  className?: string;
}

const TabsList = ({ 
  children, 
  className = '',
  activeTab,
  onTabChange
}: { 
  children: React.ReactNode, 
  className?: string,
  activeTab: string,
  onTabChange: (value: string) => void 
}) => {
  const tabs = React.useMemo(() => 
    React.Children.toArray(children).filter(
      (child): child is React.ReactElement<TabTriggerProps> =>
        React.isValidElement(child) && 
        child.type === TabsTrigger && 
        Boolean((child.props as TabTriggerProps).value)
    ),
    [children]
  );

  return (
    <div 
      className={`inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground ${className}`}
      role="tablist"
    >
      {tabs.map((tab) => {
        const tabProps = tab.props as TabTriggerProps;
        return (
          <TabsTrigger
            key={tabProps.value}
            value={tabProps.value}
            isActive={tabProps.value === activeTab}
            onClick={() => onTabChange(tabProps.value)}
          >
            {tabProps.children}
          </TabsTrigger>
        );
      })}
    </div>
  );
};

const TabsTrigger = ({ 
  children, 
  value, 
  isActive = false, 
  onClick, 
  className = '' 
}: { 
  children: React.ReactNode, 
  value: string, 
  isActive?: boolean, 
  onClick?: () => void, 
  className?: string 
}) => (
  <button
    type="button"
    role="tab"
    aria-selected={isActive}
    onClick={onClick}
    className={`inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
      isActive 
        ? 'bg-background text-foreground shadow-sm' 
        : 'text-muted-foreground hover:bg-muted hover:text-foreground'
    } ${className}`}
  >
    {children}
  </button>
);

const TabsContent = ({ 
  children, 
  value, 
  activeTab,
  className = '' 
}: { 
  children: React.ReactNode, 
  value: string, 
  activeTab: string,
  className?: string 
}) => (
  <div 
    role="tabpanel"
    className={`mt-2 ${value === activeTab ? 'block' : 'hidden'} ${className}`}
  >
    {children}
  </div>
);
// Simple card components since the UI library is not available
const Card = ({ children, className = '', ...props }: { children: React.ReactNode, className?: string }) => (
  <div className={`rounded-lg border bg-card text-card-foreground shadow-sm ${className}`} {...props}>
    {children}
  </div>
);

const CardHeader = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <div className={`flex flex-col space-y-1.5 p-6 ${className}`}>
    {children}
  </div>
);

const CardTitle = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <h3 className={`text-lg font-semibold leading-none tracking-tight ${className}`}>
    {children}
  </h3>
);

const CardContent = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <div className={`p-6 pt-0 ${className}`}>
    {children}
  </div>
);

const CardDescription = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <p className={`text-sm text-muted-foreground ${className}`}>
    {children}
  </p>
);
// Simple separator component
const Separator = ({ className = '', orientation = 'horizontal' }: { className?: string, orientation?: 'horizontal' | 'vertical' }) => (
  <div 
    className={orientation === 'horizontal' 
      ? `h-[1px] w-full bg-border ${className}` 
      : `h-full w-[1px] bg-border ${className}`} 
  />
);
// Simple loading component
const Loading = ({ text = 'Loading...', className = '' }: { text?: string, className?: string }) => (
  <div className={`flex items-center justify-center p-8 ${className}`}>
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
    {text && <span className="ml-2 text-sm text-muted-foreground">{text}</span>}
  </div>
);

// Mock data for the closed case
const mockClosedCase = {
  id: 'CASE-2023-0042',
  title: 'Product Return - Damaged Item',
  customer: {
    id: 'CUST-7890',
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Johnson & Co.'
  },
  status: 'Resolved',
  priority: 'High',
  category: 'Returns',
  assignedTo: 'Alex Chen',
  createdAt: '2023-10-15T10:30:00Z',
  closedAt: '2023-10-20T14:15:00Z',
  description: 'Customer received a damaged product and requested a return. The item was a ceramic vase that arrived broken.',
  resolution: 'Issued a full refund and provided return shipping label. Customer service followed up with a discount code for future purchases.',
  tags: ['damaged', 'refund_issued', 'customer_satisfied'],
  activities: [
    {
      id: 'act-1',
      type: 'note',
      content: 'Customer reported receiving a damaged product',
      author: 'Sarah Johnson',
      timestamp: '2023-10-15T10:30:00Z'
    },
    {
      id: 'act-2',
      type: 'status_change',
      content: 'Status changed from Open to In Progress',
      author: 'Alex Chen',
      timestamp: '2023-10-15T11:15:00Z'
    },
    {
      id: 'act-3',
      type: 'note',
      content: 'Requested photos of the damaged item',
      author: 'Alex Chen',
      timestamp: '2023-10-15T11:16:00Z'
    },
    {
      id: 'act-4',
      type: 'note',
      content: 'Photos received, confirmed damage during shipping',
      author: 'Alex Chen',
      timestamp: '2023-10-16T09:45:00Z'
    },
    {
      id: 'act-5',
      type: 'resolution',
      content: 'Issued full refund and return shipping label',
      author: 'Alex Chen',
      timestamp: '2023-10-16T10:30:00Z'
    },
    {
      id: 'act-6',
      type: 'status_change',
      content: 'Status changed from In Progress to Resolved',
      author: 'Alex Chen',
      timestamp: '2023-10-20T14:15:00Z'
    }
  ],
  attachments: [
    {
      id: 'att-1',
      name: 'damage_photo_1.jpg',
      type: 'image/jpeg',
      size: '2.4 MB',
      uploadedAt: '2023-10-15T16:30:00Z'
    },
    {
      id: 'att-2',
      name: 'refund_receipt.pdf',
      type: 'application/pdf',
      size: '0.5 MB',
      uploadedAt: '2023-10-16T10:35:00Z'
    }
  ]
};

const statusColors = {
  Open: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
  'In Progress': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
  Resolved: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
  Closed: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300',
  Reopened: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400',
};

const priorityColors = {
  Low: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300',
  Medium: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
  High: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
  Urgent: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
};

const formatDate = (dateString: string) => {
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  };
  return new Date(dateString).toLocaleDateString('en-US', options);
};

const formatTimeAgo = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) return 'just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
  return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
};

const ClosedCaseDetailPage = () => {
  const { id } = useParams();
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = React.useState(true);
  const [caseData, setCaseData] = React.useState<any>(null);
  const [activeTab, setActiveTab] = React.useState('details');
  
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  React.useEffect(() => {
    if (isAuthenticated === false) {
      router.push('/login');
    } else if (isAuthenticated === true) {
      // Simulate API call to fetch case details
      const timer = setTimeout(() => {
        setCaseData(mockClosedCase);
        setIsLoading(false);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [isAuthenticated, router, id]);

  if (isLoading || !caseData) {
    return <Loading />;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button 
            variant="outline" 
            size="icon" 
            className="h-8 w-8"
            onClick={() => router.back()}
          >
            <FiArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Case {caseData.id}</h1>
            <p className="text-muted-foreground">{caseData.title}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" className="h-8">
            <FiPrinter className="mr-2 h-4 w-4" />
            Print
          </Button>
          <Button variant="outline" size="sm" className="h-8">
            <FiShare2 className="mr-2 h-4 w-4" />
            Share
          </Button>
          <Button variant="outline" size="sm" className="h-8">
            <FiDownload className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-3">
        <div className="md:col-span-2 space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Badge className={statusColors[caseData.status as keyof typeof statusColors]}>
                    {caseData.status}
                  </Badge>
                  <Badge variant="outline" className={priorityColors[caseData.priority as keyof typeof priorityColors]}>
                    {caseData.priority} Priority
                  </Badge>
                </div>
                <div className="text-sm text-muted-foreground">
                  <span>Closed on {formatDate(caseData.closedAt)}</span>
                </div>
              </div>
            </CardHeader>
            <Separator />
            <CardContent className="pt-6">
              <Tabs value={activeTab} onValueChange={handleTabChange}>
                <TabsList 
                  className="mb-6"
                  activeTab={activeTab}
                  onTabChange={handleTabChange}
                >
                  <TabsTrigger value="details">Case Details</TabsTrigger>
                  <TabsTrigger value="activity">Activity Log</TabsTrigger>
                  <TabsTrigger value="attachments">Attachments</TabsTrigger>
                </TabsList>

                <TabsContent value="details" activeTab={activeTab} className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium mb-2">Description</h3>
                    <p className="text-muted-foreground">{caseData.description}</p>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-medium mb-2">Resolution</h3>
                    <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-100 dark:border-green-900/50">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <FiCheckCircle className="h-5 w-5 text-green-500" />
                        </div>
                        <div className="ml-3">
                          <h4 className="text-sm font-medium text-green-800 dark:text-green-200">Case Resolved</h4>
                          <p className="mt-1 text-sm text-green-700 dark:text-green-300">{caseData.resolution}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-2">Tags</h3>
                    <div className="flex flex-wrap gap-2">
                      {caseData.tags.map((tag: string) => (
                        <Badge key={tag} variant="outline" className="text-xs capitalize">
                          {tag.replace(/_/g, ' ')}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="activity" activeTab={activeTab} className="space-y-6">
                  <div className="flow-root">
                    <ul role="list" className="-mb-8">
                      {caseData.activities.map((activity: any, activityIdx: number) => (
                        <li key={activity.id}>
                          <div className="relative pb-8">
                            {activityIdx !== caseData.activities.length - 1 ? (
                              <span className="absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-700" aria-hidden="true" />
                            ) : null}
                            <div className="relative flex space-x-3">
                              <div>
                                <span className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white dark:ring-slate-800 ${
                                  activity.type === 'note' ? 'bg-blue-500' :
                                  activity.type === 'status_change' ? 'bg-purple-500' :
                                  'bg-green-500'
                                }`}>
                                  {activity.type === 'note' ? (
                                    <FiMessageSquare className="h-4 w-4 text-white" />
                                  ) : activity.type === 'status_change' ? (
                                    <FiRefreshCw className="h-4 w-4 text-white" />
                                  ) : (
                                    <FiCheck className="h-4 w-4 text-white" />
                                  )}
                                </span>
                              </div>
                              <div className="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                                <div>
                                  <p className="text-sm text-gray-700 dark:text-gray-300">
                                    <span className="font-medium text-gray-900 dark:text-white">{activity.author}</span> {activity.content}
                                  </p>
                                </div>
                                <div className="whitespace-nowrap text-right text-sm text-gray-500 dark:text-gray-400">
                                  <time dateTime={activity.timestamp}>{formatTimeAgo(activity.timestamp)}</time>
                                </div>
                              </div>
                            </div>
                          </div>
                        </li>
                      ))}
                    </ul>
                  </div>
                </TabsContent>

                <TabsContent value="attachments" activeTab={activeTab}>
                  <div className="space-y-4">
                    {caseData.attachments.length > 0 ? (
                      <ul role="list" className="divide-y divide-gray-200 dark:divide-gray-700">
                        {caseData.attachments.map((file: any) => (
                          <li key={file.id} className="py-3">
                            <div className="flex items-center space-x-4">
                              <div className="flex-shrink-0">
                                <FiFileText className="h-8 w-8 text-gray-400" />
                              </div>
                              <div className="min-w-0 flex-1">
                                <p className="truncate text-sm font-medium text-gray-900 dark:text-white">{file.name}</p>
                                <p className="truncate text-sm text-gray-500 dark:text-gray-400">{file.size} • {formatDate(file.uploadedAt)}</p>
                              </div>
                              <div>
                                <Button variant="ghost" size="sm">
                                  <FiDownload className="h-4 w-4" />
                                  <span className="sr-only">Download</span>
                                </Button>
                              </div>
                            </div>
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <div className="text-center py-6">
                        <FiFileText className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No attachments</h3>
                        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">No files have been attached to this case.</p>
                      </div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Case Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-muted-foreground">Customer</h4>
                <div className="mt-1 flex items-center">
                  <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                    <FiUser className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium">{caseData.customer.name}</p>
                    <p className="text-xs text-muted-foreground">{caseData.customer.company}</p>
                  </div>
                </div>
                <div className="mt-2 space-y-1 text-sm">
                  <div className="flex items-center">
                    <FiMail className="mr-2 h-4 w-4 text-muted-foreground" />
                    <a href={`mailto:${caseData.customer.email}`} className="text-blue-600 hover:underline dark:text-blue-400">
                      {caseData.customer.email}
                    </a>
                  </div>
                  <div className="flex items-center">
                    <FiPhone className="mr-2 h-4 w-4 text-muted-foreground" />
                    <a href={`tel:${caseData.customer.phone.replace(/\D/g, '')}`} className="text-blue-600 hover:underline dark:text-blue-400">
                      {caseData.customer.phone}
                    </a>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground">Category</h4>
                  <p className="mt-1 text-sm">{caseData.category}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground">Assigned To</h4>
                  <p className="mt-1 text-sm">{caseData.assignedTo}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground">Created</h4>
                  <p className="mt-1 text-sm">{formatDate(caseData.createdAt)}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground">Time to Resolve</h4>
                  <p className="mt-1 text-sm">
                    {Math.ceil((new Date(caseData.closedAt).getTime() - new Date(caseData.createdAt).getTime()) / (1000 * 60 * 60 * 24))} days
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" className="w-full justify-start">
                <FiEdit2 className="mr-2 h-4 w-4" />
                Reopen Case
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <FiMessageSquare className="mr-2 h-4 w-4" />
                Add Note
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <FiTag className="mr-2 h-4 w-4" />
                Edit Tags
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <FiAlertTriangle className="mr-2 h-4 w-4 text-yellow-500" />
                Report Issue
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ClosedCaseDetailPage;

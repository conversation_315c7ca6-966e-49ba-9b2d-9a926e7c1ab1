'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { Loading } from '@/components/global/Loading';
import SupplierDashboard from '@/components/dashboard/SupplierDashboard';

export default function SupplierDashboardPage() {
  const { user, isAuthenticated, logout } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  React.useEffect(() => {
    if (isAuthenticated === false) {
      router.push('/login');
    } else if (isAuthenticated === true && user) {
      // Only allow access if user is a supplier admin
      if (!user.roles || !user.roles.includes('s_admin')) {
        // If user is not a supplier admin, redirect to login
        alert('You do not have permission to access this page.');
        router.push('/login');
        return;
      }
    }
  }, [isAuthenticated, router, user]);

  if (!isAuthenticated || !user || isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Supplier Dashboard</h1>
        <Loading fullScreen={false} text="Loading dashboard data..." size="lg" className="py-12" />
      </div>
    );
  }

  return <SupplierDashboard user={user} />;
}

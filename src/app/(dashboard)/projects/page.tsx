'use client';

import React, { useState, useMemo, useCallback } from 'react';
import { ProjectCard } from '@/components/projects/ProjectCard';
import { ProjectsHeader } from '@/components/projects/ProjectsHeader';
import { CreateProjectDialog } from '@/components/projects/CreateProjectDialog';
import { Button } from '@/components/ui/button';
import { FiChevronLeft, FiChevronRight, FiPlus } from 'react-icons/fi';

// Utility function to generate a random ID
const generateId = (prefix: string) => {
  return `${prefix}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
};

// Mock data for projects
let mockProjects = [
  {
    id: generateId('PRJ'),
    name: 'Office Supplies Q2 2023',
    status: 'Active' as const,
    suppliersCount: 24,
    itemsCount: 156,
    progress: 75,
    daysRemaining: 15,
    lastUpdated: '2 hours ago',
    createdAt: new Date('2023-04-15').toISOString(),
    targetDate: '2023-06-30'
  },
  {
    id: generateId('PRJ'),
    name: 'IT Equipment Refresh',
    status: 'Active' as const,
    suppliersCount: 12,
    itemsCount: 89,
    progress: 42,
    daysRemaining: 30,
    lastUpdated: '1 day ago',
    createdAt: new Date('2023-05-01').toISOString(),
    targetDate: '2023-07-15'
  },
  {
    id: generateId('PRJ'),
    name: 'Furniture Procurement',
    status: 'Inactive' as const,
    suppliersCount: 8,
    itemsCount: 45,
    progress: 100,
    daysRemaining: 0,
    lastUpdated: '2 weeks ago',
    createdAt: new Date('2023-01-10').toISOString(),
    targetDate: '2023-03-15',
    completedAt: new Date('2023-03-10').toISOString()
  },
  {
    id: generateId('PRJ'),
    name: 'Marketing Collateral',
    status: 'Active' as const,
    suppliersCount: 5,
    itemsCount: 32,
    progress: 18,
    daysRemaining: 45,
    lastUpdated: '5 hours ago',
    createdAt: new Date('2023-05-20').toISOString(),
    targetDate: '2023-08-15'
  },
  {
    id: generateId('PRJ'),
    name: 'Company Swag 2023',
    status: 'Active' as const,
    suppliersCount: 3,
    itemsCount: 28,
    progress: 90,
    daysRemaining: 5,
    lastUpdated: '3 hours ago',
    createdAt: new Date('2023-05-15').toISOString(),
    targetDate: '2023-07-01'
  },
  {
    id: generateId('PRJ'),
    name: 'Office Renovation',
    status: 'Active' as const,
    suppliersCount: 15,
    itemsCount: 120,
    progress: 30,
    daysRemaining: 60,
    lastUpdated: '2 days ago',
    createdAt: new Date('2023-04-01').toISOString(),
    targetDate: '2023-08-30'
  },
  {
    id: generateId('PRJ'),
    name: 'Team Building Event',
    status: 'Active' as const,
    suppliersCount: 5,
    itemsCount: 15,
    progress: 60,
    daysRemaining: 20,
    lastUpdated: '1 day ago',
    createdAt: new Date('2023-05-10').toISOString(),
    targetDate: '2023-07-15'
  },
  {
    id: generateId('PRJ'),
    name: 'Software Licenses Renewal',
    status: 'Inactive' as const,
    suppliersCount: 8,
    itemsCount: 42,
    progress: 100,
    daysRemaining: 0,
    lastUpdated: '1 month ago',
    createdAt: new Date('2023-02-15').toISOString(),
    targetDate: '2023-04-30',
    completedAt: new Date('2023-04-25').toISOString()
  },
  {
    id: generateId('PRJ'),
    name: 'End of Year Gifts',
    status: 'Inactive' as const,
    suppliersCount: 4,
    itemsCount: 150,
    progress: 100,
    daysRemaining: 0,
    lastUpdated: '5 months ago',
    createdAt: new Date('2022-11-01').toISOString(),
    targetDate: '2022-12-15',
    completedAt: new Date('2022-12-10').toISOString()
  },
  {
    id: generateId('PRJ'),
    name: 'Conference Booth Setup',
    status: 'Active' as const,
    suppliersCount: 10,
    itemsCount: 35,
    progress: 75,
    daysRemaining: 10,
    lastUpdated: '1 day ago',
    createdAt: new Date('2023-04-20').toISOString(),
    targetDate: '2023-06-30'
  },
  {
    id: generateId('PRJ'),
    name: 'Cafeteria Supplies',
    status: 'Inactive' as const,
    suppliersCount: 6,
    itemsCount: 64,
    progress: 100,
    daysRemaining: 0,
    lastUpdated: '1 month ago',
    createdAt: new Date('2023-02-01').toISOString(),
    targetDate: '2023-03-31',
    completedAt: new Date('2023-03-25').toISOString()
  },
];

// Type for our project data
type Project = {
  id: string;
  name: string;
  status: 'Active' | 'Inactive';
  suppliersCount: number;
  itemsCount: number;
  progress: number;
  daysRemaining: number;
  lastUpdated: string;
  createdAt: string;
  targetDate: string;
  completedAt?: string;
  description?: string;
};

// Type for sort field
type SortField = 'name' | 'status' | 'lastUpdated' | 'progress' | 'daysRemaining';

const ProjectsPage = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortField, setSortField] = useState<SortField>('lastUpdated');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [projects, setProjects] = useState<Project[]>(mockProjects);
  const projectsPerPage = 9;

  // Filter and sort projects
  const filteredAndSortedProjects = useMemo(() => {
    let result = [...projects];
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(project => 
        project.name.toLowerCase().includes(query) ||
        project.id.toLowerCase().includes(query)
      );
    }
    
    // Apply status filter
    if (statusFilter !== 'all') {
      result = result.filter(project => 
        statusFilter === 'active' ? project.status === 'Active' : project.status === 'Inactive'
      );
    }
    
    // Apply sorting
    result.sort((a, b) => {
      let comparison = 0;
      
      switch (sortField) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'status':
          comparison = a.status.localeCompare(b.status);
          break;
        case 'progress':
          comparison = a.progress - b.progress;
          break;
        case 'daysRemaining':
          comparison = a.daysRemaining - b.daysRemaining;
          break;
        case 'lastUpdated':
        default:
          // For dates, we need to parse them
          const dateA = new Date(a.lastUpdated.includes('hour') || a.lastUpdated.includes('day') ? 
            new Date() : a.lastUpdated);
          const dateB = new Date(b.lastUpdated.includes('hour') || b.lastUpdated.includes('day') ? 
            new Date() : b.lastUpdated);
          comparison = dateB.getTime() - dateA.getTime();
      }
      
      return sortDirection === 'asc' ? comparison : -comparison;
    });
    
    return result;
  }, [projects, searchQuery, statusFilter, sortField, sortDirection]);
  
  // Calculate pagination
  const totalProjects = filteredAndSortedProjects.length;
  const totalPages = Math.ceil(totalProjects / projectsPerPage);
  const indexOfLastProject = currentPage * projectsPerPage;
  const indexOfFirstProject = indexOfLastProject - projectsPerPage;
  const currentProjects = filteredAndSortedProjects.slice(indexOfFirstProject, indexOfLastProject);
  
  // Handle page change
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);
  
  // Handle sort change
  const handleSortChange = (field: SortField, direction: 'asc' | 'desc') => {
    setSortField(field);
    setSortDirection(direction);
    setCurrentPage(1); // Reset to first page when sorting changes
  };

  const handleCreateProject = (newProject: {
    name: string;
    description: string;
    targetDate: string;
    suppliersCount: number;
    itemsCount: number;
  }) => {
    const now = new Date();
    const targetDate = new Date(newProject.targetDate);
    const daysRemaining = Math.ceil((targetDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    const project: Project = {
      id: generateId('PRJ'),
      name: newProject.name,
      status: 'Active' as const,
      suppliersCount: newProject.suppliersCount,
      itemsCount: newProject.itemsCount,
      progress: 0, // New project starts at 0%
      daysRemaining: Math.max(0, daysRemaining),
      lastUpdated: 'Just now',
      createdAt: now.toISOString(),
      targetDate: newProject.targetDate,
      description: newProject.description
    };
    
    setProjects(prev => [project, ...prev]);
    setCurrentPage(1); // Reset to first page to show the new project
  };
  
  // Generate page numbers for pagination
  const pageNumbers = [];
  const maxPageButtons = 5;
  let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
  let endPage = Math.min(totalPages, startPage + maxPageButtons - 1);
  
  if (endPage - startPage + 1 < maxPageButtons) {
    startPage = Math.max(1, endPage - maxPageButtons + 1);
  }
  
  for (let i = startPage; i <= endPage; i++) {
    pageNumbers.push(i);
  }

  return (
    <div className="flex-1 overflow-y-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Projects</h1>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <FiPlus className="mr-2 h-4 w-4" />
          Create Project
        </Button>
      </div>
      
      <div className="mb-6">
        <ProjectsHeader
          searchQuery={searchQuery}
          onSearchChange={(value) => {
            setSearchQuery(value);
            setCurrentPage(1); // Reset to first page when searching
          }}
          onFilterChange={(filter) => {
            setStatusFilter(filter);
            setCurrentPage(1); // Reset to first page when filtering
          }}
          onSortChange={handleSortChange}
          sortField={sortField}
          sortDirection={sortDirection}
          onCreateProject={() => setIsCreateDialogOpen(true)}
        />
      </div>
      
      {currentProjects.length > 0 ? (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {currentProjects.map((project) => (
              <ProjectCard key={project.id} {...project} />
            ))}
          </div>
          
          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-8 flex items-center justify-between">
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Showing <span className="font-medium">{indexOfFirstProject + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(indexOfLastProject, totalProjects)}
                </span>{' '}
                of <span className="font-medium">{totalProjects}</span> projects
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => paginate(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <FiChevronLeft className="h-4 w-4" />
                </Button>
                
                {startPage > 1 && (
                  <>
                    <button
                      onClick={() => paginate(1)}
                      className={`px-3 py-1 rounded-md text-sm font-medium ${
                        1 === currentPage
                          ? 'bg-blue-600 text-white'
                          : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
                      }`}
                    >
                      1
                    </button>
                    {startPage > 2 && <span className="px-2">...</span>}
                  </>
                )}
                
                {pageNumbers.map((number) => (
                  <button
                    key={number}
                    onClick={() => paginate(number)}
                    className={`px-3 py-1 rounded-md text-sm font-medium ${
                      number === currentPage
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
                    }`}
                  >
                    {number}
                  </button>
                ))}
                
                {endPage < totalPages && (
                  <>
                    {endPage < totalPages - 1 && <span className="px-2">...</span>}
                    <button
                      onClick={() => paginate(totalPages)}
                      className={`px-3 py-1 rounded-md text-sm font-medium ${
                        totalPages === currentPage
                          ? 'bg-blue-600 text-white'
                          : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
                      }`}
                    >
                      {totalPages}
                    </button>
                  </>
                )}
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => paginate(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  <FiChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8 text-center">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {searchQuery || statusFilter !== 'all' ? 'No matching projects found' : 'No projects yet'}
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            {searchQuery || statusFilter !== 'all' 
              ? 'Try adjusting your search or filter criteria' 
              : 'Get started by creating a new project'}
          </p>
          <Button
            onClick={() => setIsCreateDialogOpen(true)}
            className="inline-flex items-center"
          >
            <FiPlus className="mr-2 h-4 w-4" />
            Create Project
          </Button>
        </div>
      )}
      
      {/* Create Project Dialog */}
      <CreateProjectDialog
        isOpen={isCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
        onCreate={handleCreateProject}
      />
    </div>
  );
};

export default ProjectsPage;

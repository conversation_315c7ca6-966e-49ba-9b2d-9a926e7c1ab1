'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import { <PERSON><PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowLeftIcon } from '@heroicons/react/20/solid';

// Mock project data based on ID
const getProjectData = (id: string) => {
  const projectMap: Record<string, any> = {
    '0001': {
      id: '0001',
      name: 'Administration',
      description: 'Administrative tasks and management activities for the organization.',
      status: 'Active',
      budget: '$250,000',
      startDate: '2023-01-15',
      endDate: '2023-12-31',
      manager: '<PERSON>',
      team: ['<PERSON>', '<PERSON>', '<PERSON>'],
      progress: 65,
    },
    '0002': {
      id: '0002',
      name: 'Mechanical',
      description: 'Mechanical engineering and equipment maintenance projects.',
      status: 'Active',
      budget: '$450,000',
      startDate: '2023-02-01',
      endDate: '2023-11-30',
      manager: '<PERSON>',
      team: ['<PERSON>', '<PERSON>', '<PERSON>'],
      progress: 42,
    },
    '0003': {
      id: '0003',
      name: 'Civil Works',
      description: 'Infrastructure and construction projects.',
      status: 'Active',
      budget: '$1,200,000',
      startDate: '2023-03-15',
      endDate: '2024-03-15',
      manager: '<PERSON> <PERSON>',
      team: ['<PERSON>', '<PERSON> White', 'Rebecca Green'],
      progress: 28,
    },
    '0004': {
      id: '0004',
      name: 'Legal & Audit',
      description: 'Legal compliance and financial audit activities.',
      status: 'Active',
      budget: '$180,000',
      startDate: '2023-01-01',
      endDate: '2023-12-31',
      manager: 'Jennifer Lee',
      team: ['Robert Clark', 'Patricia Hall', 'James Wright'],
      progress: 75,
    },
    '0005': {
      id: '0005',
      name: 'Design',
      description: 'Product and service design initiatives.',
      status: 'Active',
      budget: '$320,000',
      startDate: '2023-04-01',
      endDate: '2023-10-31',
      manager: 'Daniel Martinez',
      team: ['Lisa Anderson', 'Kevin Thomas', 'Michelle Garcia'],
      progress: 55,
    },
  };

  return projectMap[id] || {
    id: id,
    name: 'Unknown Project',
    description: 'Project details not found',
    status: 'Unknown',
    budget: 'N/A',
    startDate: 'N/A',
    endDate: 'N/A',
    manager: 'N/A',
    team: [],
    progress: 0,
  };
};

export default function ProjectDetailPage() {
  const params = useParams();
  const projectId = params.id as string;
  const project = getProjectData(projectId);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <Link href="/projects" className="inline-flex items-center text-[#2a6e78] hover:text-[#1d4f56] transition-colors">
          <ArrowLeftIcon className="w-4 h-4 mr-2" />
          Back to Projects
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">{project.id} {project.name}</h1>
            <p className="text-gray-600 mt-1">{project.description}</p>
          </div>
          <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
            {project.status}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-sm text-gray-500 mb-1">Budget</p>
            <p className="text-lg font-semibold">{project.budget}</p>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-sm text-gray-500 mb-1">Start Date</p>
            <p className="text-lg font-semibold">{project.startDate}</p>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-sm text-gray-500 mb-1">End Date</p>
            <p className="text-lg font-semibold">{project.endDate}</p>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-sm text-gray-500 mb-1">Project Manager</p>
            <p className="text-lg font-semibold">{project.manager}</p>
          </div>
        </div>

        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Progress</h2>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div 
              className="bg-[#2a6e78] h-2.5 rounded-full" 
              style={{ width: `${project.progress}%` }}
            ></div>
          </div>
          <p className="text-right text-sm text-gray-600 mt-1">{project.progress}% Complete</p>
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">Team Members</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {project.team.map((member: string, index: number) => (
              <div key={index} className="bg-gray-50 p-4 rounded-lg flex items-center">
                <div className="w-8 h-8 bg-[#2a6e78] text-white rounded-full flex items-center justify-center mr-3">
                  {member.charAt(0)}
                </div>
                <span>{member}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="flex space-x-4">
        <Button className="bg-[#2a6e78] hover:bg-[#1d4f56]">Edit Project</Button>
        <Button variant="outline" className="border-[#2a6e78] text-[#2a6e78] hover:bg-[#e6f0f2]">
          Project Documents
        </Button>
      </div>
    </div>
  );
}

'use client';

import React, { useState } from 'react';
import styles from './test-email.module.css';

export default function TestEmailPage() {
  const [token, setToken] = useState('');
  const [templateSlug, setTemplateSlug] = useState('supplier-invitation');
  const [to, setTo] = useState('');
  const [cc, setCc] = useState('');
  const [context, setContext] = useState(JSON.stringify({
    invitationId: "test-invitation-123",
    role: "supplier",
    message: "This is a test invitation message.",
    expiryDate: "2025-07-12",
    organizationName: "Ascension Test Organization"
  }, null, 2));
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // Parse inputs
      const toEmails = to.split(',').map(email => email.trim()).filter(email => email);
      const ccEmails = cc ? cc.split(',').map(email => email.trim()).filter(email => email) : [];
      let contextObj;
      
      try {
        contextObj = JSON.parse(context);
      } catch (err) {
        throw new Error('Invalid JSON in context field');
      }

      const requestBody = {
        template_slug: templateSlug,
        to: toEmails,
        cc: ccEmails,
        context: contextObj
      };

      console.log('Sending request:', requestBody);
      
      // Get auth token from input field or localStorage (optional for local mock API)
      let authToken = token.trim();
      if (!authToken) {
        authToken = localStorage.getItem('token') || '';
        console.log('Using token from localStorage (not required for local mock API)');
      } else {
        console.log('Using token from input field');
      }
      
      // Show request details in console for debugging
      console.log('Sending request to:', '/api/email/send_email');
      
      // Send API request to local mock API
      const response = await fetch('/api/email/send_email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
          // No Authorization header needed for local mock API
        },
        body: JSON.stringify(requestBody)
      });
      
      // Check for non-200 response before trying to parse JSON
      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Could not read error details');
        throw new Error(`API returned status ${response.status}: ${errorText}`);
      }
      
      // Only try to parse JSON if response was OK
      const result = await response.json();
      
      // Display result
      setResult(result);
      console.log('API Response:', result);
    } catch (err: any) {
      console.error('Error sending test email:', err);
      setError(err.message || 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <h1 className={styles.title}>Ascension Email API Test</h1>
      <p className={styles.description}>
        Use this tool to test the email sending functionality with the local mock API.
      </p>

      <form onSubmit={handleSubmit} className={styles.form}>
        <div className={styles.formGroup}>
          <label htmlFor="token">Authentication Token (optional for local mock API):</label>
          <input
            type="text"
            id="token"
            value={token}
            onChange={(e) => setToken(e.target.value)}
            placeholder="JWT token (optional for local testing)"
            className={styles.input}
          />
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="templateSlug">Template Slug:</label>
          <select
            id="templateSlug"
            value={templateSlug}
            onChange={(e) => setTemplateSlug(e.target.value)}
            className={styles.select}
          >
            <option value="supplier-invitation">supplier-invitation</option>
            <option value="welcome">welcome</option>
            <option value="password-reset">password-reset</option>
            <option value="custom-template">custom-template</option>
          </select>
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="to">To (comma separated):</label>
          <input
            type="text"
            id="to"
            value={to}
            onChange={(e) => setTo(e.target.value)}
            placeholder="<EMAIL>, <EMAIL>"
            className={styles.input}
            required
          />
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="cc">CC (comma separated):</label>
          <input
            type="text"
            id="cc"
            value={cc}
            onChange={(e) => setCc(e.target.value)}
            placeholder="<EMAIL>, <EMAIL>"
            className={styles.input}
          />
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="context">Context (JSON):</label>
          <textarea
            id="context"
            value={context}
            onChange={(e) => setContext(e.target.value)}
            rows={10}
            className={styles.textarea}
            required
          />
        </div>
        
        <button type="submit" className={styles.button} disabled={loading}>
          {loading ? 'Sending...' : 'Send Test Email'}
        </button>
      </form>
      
      {error && (
        <div className={styles.error}>
          <h3>❌ Error</h3>
          <p>{error}</p>
          <p>Check the browser console for more details.</p>
        </div>
      )}
      
      {result && (
        <div className={styles.result}>
          <h3>✅ API Response:</h3>
          <pre>{JSON.stringify(result, null, 2)}</pre>
        </div>
      )}
      
      <div className={styles.instructions}>
        <h3>Instructions</h3>
        <ol>
          <li>Enter recipient email addresses (comma separated)</li>
          <li>Modify the context JSON if needed</li>
          <li>Click &quot;Send Test Email&quot;</li>
          <li>Check the result below and browser console for detailed logs</li>
        </ol>
        <p><strong>Note:</strong> This is using the local mock API. Emails will be logged but not actually sent.</p>
      </div>
    </div>
  );
}

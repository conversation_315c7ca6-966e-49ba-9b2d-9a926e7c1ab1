.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.title {
  color: #333;
  margin-bottom: 0.5rem;
}

.description {
  color: #666;
  margin-bottom: 2rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.formGroup label {
  font-weight: 500;
  color: #333;
}

.input, .select, .textarea {
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  width: 100%;
}

.textarea {
  font-family: monospace;
  min-height: 200px;
}

.button {
  background-color: #0070f3;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.button:hover {
  background-color: #0051a8;
}

.button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.error {
  background-color: #fff2f2;
  border: 1px solid #ffcfcf;
  border-radius: 4px;
  padding: 1rem;
  margin-bottom: 2rem;
  color: #d32f2f;
}

.result {
  background-color: #f2f8ff;
  border: 1px solid #cfe8ff;
  border-radius: 4px;
  padding: 1rem;
  margin-bottom: 2rem;
}

.result pre {
  background-color: #f5f5f5;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  font-family: monospace;
}

.instructions {
  background-color: #f9f9f9;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 1rem;
}

.instructions h3 {
  margin-top: 0;
}

.instructions ol {
  padding-left: 1.5rem;
}

.instructions p {
  margin-bottom: 0;
}

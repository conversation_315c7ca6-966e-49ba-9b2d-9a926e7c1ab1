import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import EmailTemplate from '@/models/EmailTemplate';

export async function GET(request: NextRequest) {
  try {
    await dbConnect();

    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    const query: any = {};
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { slug: { $regex: search, $options: 'i' } },
        { subject: { $regex: search, $options: 'i' } },
      ];
    }

    const total = await EmailTemplate.countDocuments(query);
    const templates = await EmailTemplate.find(query)
      .sort({ updatedAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .lean();

    return NextResponse.json({
      status: 'success',
      data: {
        items: templates,
        total,
        page,
        limit,
        total_pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Failed to fetch email templates:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { status: 'error', error: { code: 'INTERNAL_SERVER_ERROR', message: errorMessage } },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    await dbConnect();
    const body = await request.json();

    // Basic validation
    const { name, slug, subject, body: html, variables } = body;
    if (!name || !slug || !subject || !html) {
        return NextResponse.json(
            { error: { code: 'VALIDATION_ERROR', message: 'Missing required fields: name, slug, subject, body.' } },
            { status: 400 }
        );
    }

    const newTemplate = await EmailTemplate.create({
      name,
      slug,
      subject,
      body: html,
      variables: variables || [],
      is_active: body.is_active !== undefined ? body.is_active : true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });

    return NextResponse.json({ status: 'success', data: newTemplate }, { status: 201 });

  } catch (error: any) {
    console.error('Failed to create email template:', error);
    if (error.code === 11000) {
      return NextResponse.json(
        { error: { code: 'DUPLICATE_SLUG', message: `A template with slug '${error.keyValue.slug}' already exists.` } },
        { status: 409 }
      );
    }
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { status: 'error', error: { code: 'INTERNAL_SERVER_ERROR', message: errorMessage } },
      { status: 500 }
    );
  }
}

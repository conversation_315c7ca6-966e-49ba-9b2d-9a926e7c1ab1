import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import EmailTemplate from '@/models/EmailTemplate';

// A simple placeholder replacement for preview
const getSampleData = () => ({
  'user.name': '<PERSON>',
  'user.email': '<EMAIL>',
  'organization.name': 'Ascension Inc.',
  'action.url': '#',
  'invitation.url': '#',
  'reset.url': '#',
  date: new Date().toLocaleDateString(),
  year: new Date().getFullYear().toString(),
});

const replacePlaceholders = (text: string, data: Record<string, string>) => {
  let result = text;
  for (const [key, value] of Object.entries(data)) {
    const regex = new RegExp(`{{\s*${key}\s*}}`, 'g');
    result = result.replace(regex, value);
  }
  return result;
};

export async function GET(
  request: Request,
  { params }: { params: { slug: string } }
) {
  const { slug } = params;

  try {
    await dbConnect();

    const template = await EmailTemplate.findOne({ slug }).lean();

    if (!template) {
      return new NextResponse(
        `<h1>Template not found</h1><p>A template with the slug '<code>${slug}</code>' could not be found.</p>`,
        {
          status: 404,
          headers: { 'Content-Type': 'text/html' },
        }
      );
    }

    const sampleData = getSampleData();
    let body = replacePlaceholders(template.body, sampleData);
    
    if (template.signature) {
      const signature = replacePlaceholders(template.signature, sampleData);
      if (template.is_html) {
        body += `<br><br>${signature.replace(/\n/g, '<br>')}`;
      } else {
        body += `\n\n${signature}`;
      }
    }

    if (template.is_html) {
      return new NextResponse(body, {
        status: 200,
        headers: { 'Content-Type': 'text/html' },
      });
    } else {
      // For plain text, wrap in <pre> for better viewing in a browser
      const htmlContent = `<html><head><title>${template.subject}</title></head><body><pre style="font-family: monospace; white-space: pre-wrap;">${body}</pre></body></html>`;
      return new NextResponse(htmlContent, {
        status: 200,
        headers: { 'Content-Type': 'text/html' },
      });
    }
  } catch (error) {
    console.error('Error fetching template preview:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return new NextResponse(
      `<h1>Internal Server Error</h1><p>${errorMessage}</p>`,
      {
        status: 500,
        headers: { 'Content-Type': 'text/html' },
      }
    );
  }
}

// Simple test script using native fetch
// This script is designed to work in modern Node.js environments

// Use dynamic import for fetch
async function runTest() {
  try {
    console.log('Testing email sending API...');
    
    // Create the request body
    const requestBody = {
      template_slug: 'supplier-invitation',
      to: ['<EMAIL>', '<EMAIL>'],
      cc: ['<EMAIL>'],
      context: {
        invitationId: 'test-invitation-123',
        role: 'supplier',
        message: 'This is a test invitation message.',
        expiryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString()
      }
    };
    
    console.log('Request body:', JSON.stringify(requestBody, null, 2));
    
    // Make the API call
    const response = await fetch('https://dev.ascensionservices.net/api/email/send_email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_AUTH_TOKEN_HERE' // Replace with actual token
      },
      body: JSON.stringify(requestBody)
    });
    
    // Check response status
    console.log('Response status:', response.status);
    
    // Parse response body
    const responseData = await response.json();
    console.log('Response data:', JSON.stringify(responseData, null, 2));
    
    // Check if successful
    if (responseData.status === 'success') {
      console.log('✅ Email sending test successful!');
    } else {
      console.error('❌ Email sending test failed:', 
        responseData.error?.message || 'Unknown error');
    }
  } catch (error) {
    console.error('Error during test:', error);
  }
}

// Run the test
runTest();

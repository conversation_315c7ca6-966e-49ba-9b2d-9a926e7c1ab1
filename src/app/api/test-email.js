// Simple JavaScript version of the email test script
const fetch = require('node-fetch');

/**
 * Test script to verify email sending functionality
 */
async function testEmailSending() {
  try {
    console.log('Testing email sending API...');
    
    // Get auth token from local storage if running in browser environment
    let authToken = '';
    if (typeof localStorage !== 'undefined') {
      authToken = localStorage.getItem('token') || '';
    }
    
    const response = await fetch('https://dev.ascensionservices.net/api/email/send_email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        template_slug: 'supplier-invitation',
        to: ['<EMAIL>', '<EMAIL>'],
        cc: ['<EMAIL>'],
        context: {
          invitationId: 'test-invitation-123',
          role: 'supplier',
          message: 'This is a test invitation message.',
          expiryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString()
        }
      })
    });
    
    if (!response.ok) {
      throw new Error(`API returned status ${response.status}`);
    }
    
    const result = await response.json();
    
    console.log('API Response:', JSON.stringify(result, null, 2));
    
    if (result.status === 'success') {
      console.log('✅ Email sending test successful!');
      console.log(`Email ID: ${result.data?.email_id}`);
      console.log(`Recipients: ${result.data?.recipients?.total} total`);
    } else {
      console.error('❌ Email sending test failed:', result.error?.message || 'Unknown error');
    }
  } catch (error) {
    console.error('Error testing email API:', error);
  }
}

// Run the test
testEmailSending();

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Ascension Email API Test</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #333;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input, textarea, select {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 16px;
    }
    button {
      background-color: #4CAF50;
      color: white;
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }
    button:hover {
      background-color: #45a049;
    }
    .result {
      margin-top: 20px;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background-color: #f9f9f9;
      white-space: pre-wrap;
    }
    .success {
      color: #4CAF50;
      font-weight: bold;
    }
    .error {
      color: #f44336;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <h1>Ascension Email API Test</h1>
  <p>Use this tool to test the email sending functionality with the actual backend API.</p>
  
  <div class="form-group">
    <label for="token">Authentication Token:</label>
    <input type="text" id="token" placeholder="Enter your JWT token here">
    <small>Leave empty to use token from localStorage</small>
  </div>
  
  <div class="form-group">
    <label for="template">Template Slug:</label>
    <select id="template">
      <option value="supplier-invitation">supplier-invitation</option>
      <option value="password-reset">password-reset</option>
    </select>
  </div>
  
  <div class="form-group">
    <label for="to">To (comma separated):</label>
    <input type="text" id="to" value="<EMAIL>, <EMAIL>">
  </div>
  
  <div class="form-group">
    <label for="cc">CC (comma separated):</label>
    <input type="text" id="cc" value="<EMAIL>">
  </div>
  
  <div class="form-group">
    <label for="context">Context (JSON):</label>
    <textarea id="context" rows="10">{
  "invitationId": "test-invitation-123",
  "role": "supplier",
  "message": "This is a test invitation message.",
  "expiryDate": "2025-07-12"
}</textarea>
  </div>
  
  <button id="sendBtn">Send Test Email</button>
  
  <div class="result" id="result" style="display: none;"></div>
  
  <div class="form-group" style="margin-top: 30px;">
    <h3>Instructions</h3>
    <ol>
      <li>Enter your authentication token or ensure you're logged in to the application</li>
      <li>Select the template you want to test</li>
      <li>Enter recipient email addresses (comma separated)</li>
      <li>Modify the context JSON if needed</li>
      <li>Click "Send Test Email"</li>
      <li>Check the result below and browser console for detailed logs</li>
    </ol>
    <p><strong>Note:</strong> This is a test tool that sends real emails through the backend API. The emails will be delivered to the specified recipients.</p>
  </div>
  
  <script>
    document.getElementById('sendBtn').addEventListener('click', async () => {
      const resultDiv = document.getElementById('result');
      resultDiv.innerHTML = 'Sending email...';
      resultDiv.style.display = 'block';
      resultDiv.className = 'result';
      
      try {
        // Get form values
        const templateSlug = document.getElementById('template').value;
        const to = document.getElementById('to').value.split(',').map(email => email.trim());
        const cc = document.getElementById('cc').value ? document.getElementById('cc').value.split(',').map(email => email.trim()) : [];
        const context = JSON.parse(document.getElementById('context').value);
        
        // Prepare request body
        const requestBody = {
          template_slug: templateSlug,
          to,
          cc,
          context
        };
        
        console.log('Sending request:', requestBody);
        
        // Get auth token from input field or localStorage (optional for local mock API)
        let authToken = document.getElementById('token').value.trim();
        if (!authToken) {
          authToken = localStorage.getItem('token') || '';
          console.log('Using token from localStorage (not required for local mock API)');
        } else {
          console.log('Using token from input field');
        }
        
        // Show request details in console for debugging
        console.log('Sending request to:', '/api/email/send_email');
        
        // Send API request to local mock API
        const response = await fetch('/api/email/send_email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
            // No Authorization header needed for local mock API
          },
          body: JSON.stringify(requestBody)
        });
        
        // Check for non-200 response before trying to parse JSON
        if (!response.ok) {
          const errorText = await response.text().catch(() => 'Could not read error details');
          throw new Error(`API returned status ${response.status}: ${errorText}`);
        }
        
        // Only try to parse JSON if response was OK
        const result = await response.json();
        
        // Display result
        resultDiv.innerHTML = `<h3>API Response:</h3><pre>${JSON.stringify(result, null, 2)}</pre>`;
        
        if (result.status === 'success') {
          resultDiv.innerHTML += `
            <div class="success">
              <h3>✅ Email sent successfully!</h3>
              <p><strong>Email ID:</strong> ${result.data?.email_id || 'N/A'}</p>
              <p><strong>Recipients:</strong> ${result.data?.recipients?.total || 0} total</p>
              <p><strong>Template:</strong> ${result.data?.template || 'N/A'}</p>
              <p>Check your inbox to confirm the email was delivered.</p>
            </div>
          `;
          resultDiv.classList.add('success');
        } else {
          resultDiv.innerHTML += `
            <div class="error">
              <h3>❌ Email sending failed!</h3>
              <p><strong>Error:</strong> ${result.error?.message || 'Unknown error'}</p>
              <p><strong>Code:</strong> ${result.error?.code || 'N/A'}</p>
            </div>
          `;
          resultDiv.classList.add('error');
        }
      } catch (error) {
        console.error('Error during API call:', error);
        resultDiv.innerHTML = `
          <div class="error">
            <h3>❌ Error</h3>
            <p><strong>Message:</strong> ${error.message}</p>
            <p>Check the browser console for more details.</p>
          </div>
        `;
        resultDiv.classList.add('error');
      }
    });
  </script>
</body>
</html>

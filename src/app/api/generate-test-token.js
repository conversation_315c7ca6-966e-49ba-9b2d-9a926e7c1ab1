// Simple script to generate a test JWT token
// Run this with: node src/app/api/generate-test-token.js

/**
 * Generate a simple JWT token for testing
 * Note: This is NOT a secure token and should only be used for testing
 */
function generateTestToken() {
  // Create a simple header
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };
  
  // Create a payload with some test data
  const payload = {
    sub: 'test-user-123',
    name: 'Test User',
    email: '<EMAIL>',
    role: 'admin',
    permissions: ['read:all', 'write:all'],
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour from now
  };
  
  // Encode the header and payload
  const encodedHeader = Buffer.from(JSON.stringify(header)).toString('base64')
    .replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');
  
  const encodedPayload = Buffer.from(JSON.stringify(payload)).toString('base64')
    .replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');
  
  // For a real JWT, we would sign this with a secret key
  // But for testing, we'll just use a dummy signature
  const signature = 'test_signature_for_development_only';
  const encodedSignature = Buffer.from(signature).toString('base64')
    .replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');
  
  // Combine to form the JWT
  const token = `${encodedHeader}.${encodedPayload}.${encodedSignature}`;
  
  return token;
}

// Generate and print the token
const token = generateTestToken();
console.log('\n=== TEST JWT TOKEN ===');
console.log(token);
console.log('\nCopy this token to use in your email test page.');
console.log('Remember: This is only a test token and will not work for actual authentication.');
console.log('======================\n');

import { NextRequest, NextResponse } from 'next/server';
import { generateVerificationToken, sendVerificationEmail } from '../verify-email/route';

// Mock database for rate limiting - replace with actual database/cache
const resendAttempts = new Map<string, { count: number; lastAttempt: Date }>();

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email address is required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email address format' },
        { status: 400 }
      );
    }

    // Rate limiting: Allow max 3 attempts per hour per email
    const now = new Date();
    const attempts = resendAttempts.get(email);
    
    if (attempts) {
      const hourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      
      if (attempts.lastAttempt > hourAgo && attempts.count >= 3) {
        return NextResponse.json(
          { 
            error: 'RATE_LIMITED', 
            message: 'Too many verification emails sent. Please wait an hour before requesting another.' 
          },
          { status: 429 }
        );
      }
      
      // Reset count if last attempt was more than an hour ago
      if (attempts.lastAttempt <= hourAgo) {
        attempts.count = 0;
      }
    }

    // In a real application, you would:
    // 1. Check if the email exists in your user database
    // 2. Check if the email is already verified
    // 3. Invalidate any existing verification tokens for this email

    // For demo purposes, we'll assume the email exists and needs verification
    console.log(`Resending verification email for: ${email}`);

    // Generate new verification token
    const token = generateVerificationToken(email);

    // Send verification email
    const emailSent = await sendVerificationEmail(email, token);

    if (!emailSent) {
      return NextResponse.json(
        { error: 'SEND_FAILED', message: 'Failed to send verification email' },
        { status: 500 }
      );
    }

    // Update rate limiting
    const currentAttempts = resendAttempts.get(email) || { count: 0, lastAttempt: new Date(0) };
    resendAttempts.set(email, {
      count: currentAttempts.count + 1,
      lastAttempt: now
    });

    return NextResponse.json(
      { 
        success: true, 
        message: 'Verification email sent successfully',
        email 
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Resend verification error:', error);
    return NextResponse.json(
      { error: 'INTERNAL_ERROR', message: 'Internal server error' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';

// Mock database for demonstration - replace with actual database
const mockVerificationTokens = new Map<string, {
  email: string;
  expiresAt: Date;
  used: boolean;
}>();

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { error: 'Verification token is required' },
        { status: 400 }
      );
    }

    // Check if token exists in mock database
    const tokenData = mockVerificationTokens.get(token);

    if (!tokenData) {
      return NextResponse.json(
        { error: 'INVALID_TOKEN', message: 'Invalid verification token' },
        { status: 400 }
      );
    }

    // Check if token is expired
    if (new Date() > tokenData.expiresAt) {
      return NextResponse.json(
        { error: 'TOKEN_EXPIRED', message: 'Verification token has expired' },
        { status: 400 }
      );
    }

    // Check if token has already been used
    if (tokenData.used) {
      return NextResponse.json(
        { error: 'TOKEN_USED', message: 'Verification token has already been used' },
        { status: 400 }
      );
    }

    // Mark token as used
    tokenData.used = true;
    mockVerificationTokens.set(token, tokenData);

    // In a real application, you would:
    // 1. Update the user's email_verified status in the database
    // 2. Remove the verification token from the database
    // 3. Log the verification event

    console.log(`Email verified for: ${tokenData.email}`);

    return NextResponse.json(
      { 
        success: true, 
        message: 'Email verified successfully',
        email: tokenData.email 
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Email verification error:', error);
    return NextResponse.json(
      { error: 'INTERNAL_ERROR', message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper function to generate verification token (for use in signup)
export function generateVerificationToken(email: string): string {
  const token = Math.random().toString(36).substring(2) + Date.now().toString(36);
  const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours from now

  // Store token in mock database
  mockVerificationTokens.set(token, {
    email,
    expiresAt,
    used: false
  });

  return token;
}

// Helper function to send verification email using SMTP directly
export async function sendVerificationEmail(email: string, token: string): Promise<boolean> {
  try {
    const verificationUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/verify-email?token=${token}&email=${encodeURIComponent(email)}`;

    // Create the email HTML content
    const emailHtml = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Verify Your Email - Ascension</title>
          <style>
              body { margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f5f5f5; }
              .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
              .header { background-color: #18546c; padding: 30px 20px; text-align: center; }
              .header h1 { color: white; margin: 0; font-size: 28px; font-weight: bold; }
              .content { padding: 40px 30px; background-color: #ffffff; }
              .content h2 { color: #333333; margin: 0 0 20px 0; font-size: 24px; }
              .content p { color: #666666; line-height: 1.6; margin: 0 0 20px 0; font-size: 16px; }
              .button-container { text-align: center; margin: 30px 0; }
              .verify-button { background-color: #18546c; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold; font-size: 16px; }
              .link-text { color: #18546c; word-break: break-all; font-size: 14px; background-color: #f9f9f9; padding: 10px; border-radius: 3px; margin: 10px 0; }
              .footer { background-color: #333333; padding: 20px; text-align: center; }
              .footer p { color: #999999; margin: 0; font-size: 12px; }
              .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }
              .warning p { color: #856404; margin: 0; font-size: 14px; }
          </style>
      </head>
      <body>
          <div class="container">
              <div class="header">
                  <h1>Ascension</h1>
              </div>
              <div class="content">
                  <h2>Verify Your Email Address</h2>
                  <p>Hello,</p>
                  <p>Thank you for signing up for Ascension! To complete your registration and secure your account, please verify your email address by clicking the button below.</p>
                  <div class="button-container">
                      <a href="${verificationUrl}" class="verify-button">Verify Email Address</a>
                  </div>
                  <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                  <div class="link-text">${verificationUrl}</div>
                  <div class="warning">
                      <p><strong>Important:</strong> This verification link will expire in 24 hours. If you didn't create an account with Ascension, you can safely ignore this email.</p>
                  </div>
                  <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
                  <p>Best regards,<br>The Ascension Team</p>
              </div>
              <div class="footer">
                  <p>© 2024 Ascension. All rights reserved.</p>
                  <p>This email was sent to ${email}. If you didn't request this email, please ignore it.</p>
              </div>
          </div>
      </body>
      </html>
    `;

    // Use the existing email sending API
    const emailResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/email/send_email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to: [email],
        subject: 'Verify your Ascension account',
        html: emailHtml
      })
    });

    if (emailResponse.ok) {
      console.log(`Verification email sent successfully to: ${email}`);
      console.log('Verification URL:', verificationUrl);
      return true;
    } else {
      console.error('Failed to send verification email via API:', await emailResponse.text());

      // Fallback: Log the email content for development
      console.log('Verification email fallback - would be sent to:', email);
      console.log('Verification URL:', verificationUrl);

      // Return true for development purposes
      return true;
    }
  } catch (error) {
    console.error('Failed to send verification email:', error);

    // Fallback for development
    console.log('Verification email fallback - would be sent to:', email);
    console.log('Verification URL:', `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/verify-email?token=${token}&email=${encodeURIComponent(email)}`);

    // Return true for development purposes
    return true;
  }
}

import { NextRequest, NextResponse } from 'next/server';

// Mock database for demonstration - replace with actual database
const mockVerificationTokens = new Map<string, {
  email: string;
  expiresAt: Date;
  used: boolean;
}>();

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { error: 'Verification token is required' },
        { status: 400 }
      );
    }

    // Check if token exists in mock database
    const tokenData = mockVerificationTokens.get(token);

    if (!tokenData) {
      return NextResponse.json(
        { error: 'INVALID_TOKEN', message: 'Invalid verification token' },
        { status: 400 }
      );
    }

    // Check if token is expired
    if (new Date() > tokenData.expiresAt) {
      return NextResponse.json(
        { error: 'TOKEN_EXPIRED', message: 'Verification token has expired' },
        { status: 400 }
      );
    }

    // Check if token has already been used
    if (tokenData.used) {
      return NextResponse.json(
        { error: 'TOKEN_USED', message: 'Verification token has already been used' },
        { status: 400 }
      );
    }

    // Mark token as used
    tokenData.used = true;
    mockVerificationTokens.set(token, tokenData);

    // In a real application, you would:
    // 1. Update the user's email_verified status in the database
    // 2. Remove the verification token from the database
    // 3. Log the verification event

    console.log(`Email verified for: ${tokenData.email}`);

    return NextResponse.json(
      { 
        success: true, 
        message: 'Email verified successfully',
        email: tokenData.email 
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Email verification error:', error);
    return NextResponse.json(
      { error: 'INTERNAL_ERROR', message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper function to generate verification token (for use in signup)
export function generateVerificationToken(email: string): string {
  const token = Math.random().toString(36).substring(2) + Date.now().toString(36);
  const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours from now

  // Store token in mock database
  mockVerificationTokens.set(token, {
    email,
    expiresAt,
    used: false
  });

  return token;
}

// Helper function to send verification email (mock implementation)
export async function sendVerificationEmail(email: string, token: string): Promise<boolean> {
  try {
    // In a real application, you would use a service like:
    // - SendGrid
    // - AWS SES
    // - Nodemailer with SMTP
    // - Resend
    // - Mailgun

    const verificationUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/verify-email?token=${token}&email=${encodeURIComponent(email)}`;

    // Mock email content
    const emailContent = {
      to: email,
      subject: 'Verify your Ascension account',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #18546c; padding: 20px; text-align: center;">
            <h1 style="color: white; margin: 0;">Ascension</h1>
          </div>
          <div style="padding: 30px; background-color: #f9f9f9;">
            <h2 style="color: #333;">Verify Your Email Address</h2>
            <p style="color: #666; line-height: 1.6;">
              Thank you for signing up for Ascension! To complete your registration, please verify your email address by clicking the button below.
            </p>
            <div style="text-align: center; margin: 30px 0;">
              <a href="${verificationUrl}" style="background-color: #18546c; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                Verify Email Address
              </a>
            </div>
            <p style="color: #666; font-size: 14px;">
              If the button doesn't work, you can copy and paste this link into your browser:
            </p>
            <p style="color: #18546c; word-break: break-all; font-size: 14px;">
              ${verificationUrl}
            </p>
            <p style="color: #666; font-size: 14px;">
              This link will expire in 24 hours. If you didn't create an account with Ascension, you can safely ignore this email.
            </p>
          </div>
          <div style="background-color: #333; padding: 20px; text-align: center;">
            <p style="color: #999; margin: 0; font-size: 12px;">
              © 2024 Ascension. All rights reserved.
            </p>
          </div>
        </div>
      `
    };

    // Log the email content for development
    console.log('Verification email would be sent:', emailContent);
    console.log('Verification URL:', verificationUrl);

    // In production, replace this with actual email sending logic
    // Example with a hypothetical email service:
    // await emailService.send(emailContent);

    return true;
  } catch (error) {
    console.error('Failed to send verification email:', error);
    return false;
  }
}

import { NextRequest, NextResponse } from 'next/server';

// Mock database for invitations
let invitations: any[] = [];

export async function GET(request: NextRequest) {
  // Get query parameters
  const searchParams = request.nextUrl.searchParams;
  const status = searchParams.get('status');
  const organization_id = searchParams.get('organization_id');
  const page = parseInt(searchParams.get('page') || '1');
  const limit = parseInt(searchParams.get('limit') || '10');
  
  // Filter invitations based on query parameters
  let filteredInvitations = [...invitations];
  
  if (status) {
    filteredInvitations = filteredInvitations.filter(inv => inv.status === status);
  }
  
  if (organization_id) {
    filteredInvitations = filteredInvitations.filter(inv => inv.organization_id === organization_id);
  }
  
  // Paginate results
  const startIndex = (page - 1) * limit;
  const endIndex = page * limit;
  const paginatedInvitations = filteredInvitations.slice(startIndex, endIndex);
  
  return NextResponse.json({
    status: 'success',
    data: {
      items: paginatedInvitations,
      total: filteredInvitations.length,
      page,
      limit,
      total_pages: Math.ceil(filteredInvitations.length / limit)
    }
  }, { headers: { 'Content-Type': 'application/json' } });
}

export async function POST(request: NextRequest) {
  // Set proper content type header
  const headers = new Headers();
  headers.append('Content-Type', 'application/json');
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.email) {
      return NextResponse.json({
        status: 'error',
        error: {
          code: 'MISSING_FIELD',
          message: 'Email is required'
        }
      }, { status: 400, headers: { 'Content-Type': 'application/json' } });
    }
    
    if (!body.role) {
      return NextResponse.json({
        status: 'error',
        error: {
          code: 'MISSING_FIELD',
          message: 'Role is required'
        }
      }, { status: 400, headers: { 'Content-Type': 'application/json' } });
    }
    
    // Create a new invitation
    const newInvitation = {
      id: `inv_${Math.random().toString(36).substring(2, 15)}`,
      email: body.email,
      role: body.role,
      organization_id: body.organization_id || 'current',
      status: 'pending',
      created_at: new Date().toISOString(),
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
      email_sent: false,
      email_error: '',
      email_id: ''
    };
    
    // Add to mock database
    invitations.push(newInvitation);
    
    // Send the invitation email using the email API endpoint according to API docs
    try {
      // Prepare the email request according to API documentation
      const emailRequest = {
        template_slug: 'supplier-invitation',
        to: [body.email],
        context: {
          // Include all necessary variables for the template
          invitationId: newInvitation.id,
          role: body.role,
          message: body.message || 'You have been invited to join Ascension as a supplier.',
          expiryDate: new Date(newInvitation.expires_at).toLocaleDateString(),
          // Add any other context variables needed by the template
          organizationName: 'Ascension',
          invitationUrl: `https://app.ascensionservices.net/accept-invitation/${newInvitation.id}`
        }
      };
      
      console.log('Sending invitation email with request:', emailRequest);
      
      // Call the email sending API
      const emailResponse = await fetch(`${request.nextUrl.origin}/api/email/send_email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(emailRequest)
      });
      
      // Handle non-OK responses
      if (!emailResponse.ok) {
        const errorText = await emailResponse.text();
        throw new Error(`Email API returned status ${emailResponse.status}: ${errorText}`);
      }
      
      // Parse the response
      const emailResult = await emailResponse.json();
      console.log('Email API response:', emailResult);
      
      if (emailResult.status !== 'success') {
        // Email API returned an error
        console.error('Failed to send invitation email:', emailResult.error);
        // Add a note to the invitation that email failed
        newInvitation.email_sent = false;
        newInvitation.email_error = emailResult.error?.message || 'Unknown error';
      } else {
        // Email was sent successfully
        newInvitation.email_sent = true;
        newInvitation.email_id = emailResult.data?.email_id;
        console.log(`✅ Email sent successfully to ${body.email}`);
      }
    } catch (emailError: unknown) {
      console.error('Error sending invitation email:', emailError);
      // Add a note to the invitation that email failed
      newInvitation.email_sent = false;
      newInvitation.email_error = emailError instanceof Error ? emailError.message : 'Unknown error';
    }
    
    // Return success response
    return NextResponse.json({
      status: 'success',
      data: newInvitation,
      message: 'Invitation created successfully'
    }, { headers: { 'Content-Type': 'application/json' } });
  } catch (error: any) {
    return NextResponse.json({
      status: 'error',
      error: {
        code: 'SERVER_ERROR',
        message: error.message || 'An unexpected error occurred'
      }
    }, { status: 500, headers: { 'Content-Type': 'application/json' } });
  }
}

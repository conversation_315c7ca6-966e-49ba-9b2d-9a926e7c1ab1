import { NextResponse } from 'next/server';
import { connectDB } from '@/lib/db';
import EmailTemplate from '@/models/EmailTemplate';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// Initialize database connection
await connectDB();

// GET /api/email-templates - Get all email templates
export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const templates = await EmailTemplate.find().sort({ name: 1 });
    return NextResponse.json(templates);
  } catch (error) {
    console.error('Error fetching email templates:', error);
    return NextResponse.json(
      { error: 'Failed to fetch email templates' },
      { status: 500 }
    );
  }
}

// POST /api/email-templates - Create a new email template
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const data = await request.json();
    
    // Validate required fields
    if (!data.name || !data.slug || !data.subject || !data.html || !data.text) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if template with same slug exists
    const existingTemplate = await EmailTemplate.findOne({ slug: data.slug });
    if (existingTemplate) {
      return NextResponse.json(
        { error: 'Template with this slug already exists' },
        { status: 400 }
      );
    }

    // Extract variables from template
    const variables = [...new Set([
      ...(data.html.match(/{{(.*?)}}/g) || []).map((v: string) => v.replace(/[{}]/g, '')),
      ...(data.text.match(/{{(.*?)}}/g) || []).map((v: string) => v.replace(/[{}]/g, ''))
    ])];

    const template = new EmailTemplate({
      ...data,
      variables,
      isActive: data.isActive !== undefined ? data.isActive : true
    });

    await template.save();
    return NextResponse.json(template, { status: 201 });
  } catch (error) {
    console.error('Error creating email template:', error);
    return NextResponse.json(
      { error: 'Failed to create email template' },
      { status: 500 }
    );
  }
}

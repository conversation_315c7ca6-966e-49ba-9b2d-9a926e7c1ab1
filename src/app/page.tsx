'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import { Logo } from '@/components/logo';

export default function HomePage() {
  const { user } = useAuth();

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex items-center justify-between">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Image 
                src="/images/logo/light.png" 
                alt="Ascension Logo" 
                width={180} 
                height={40} 
                className="h-10 w-auto"
              />
            </div>
          </div>
          <nav className="hidden md:flex space-x-8">
            <Link href="/" className="text-gray-900 hover:text-[#14546c] px-3 py-2 text-sm font-medium">
              Home
            </Link>
            <Link href="#solutions" className="text-gray-900 hover:text-[#14546c] px-3 py-2 text-sm font-medium">
              Solutions
            </Link>
            <Link href="#pricing" className="text-gray-900 hover:text-[#14546c] px-3 py-2 text-sm font-medium">
              Pricing
            </Link>
            <Link href="#about" className="text-gray-900 hover:text-[#14546c] px-3 py-2 text-sm font-medium">
              About
            </Link>
            <Link href="#contact" className="text-gray-900 hover:text-[#14546c] px-3 py-2 text-sm font-medium">
              Contact
            </Link>
          </nav>
          <div>
            {user ? (
              <Link 
                href="/dashboard" 
                className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-[#14546c] hover:bg-[#0e3e50]"
              >
                Customer Portal
              </Link>
            ) : (
              <Link 
                href="/login" 
                className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-[#14546c] hover:bg-[#0e3e50]"
              >
                Customer Portal
              </Link>
            )}
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-24">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Streamline your company<br />procurement system
            </h1>
            <p className="max-w-2xl mx-auto text-lg text-gray-600 mb-8">
              The ideal procurement solution for your company or business. Our platform is
              the ultimate solution to corporate commerce with simple intuitive tools that
              make B2B trading seamless.
            </p>
            <Link 
              href="#learn-more" 
              className="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-[#14546c] hover:bg-[#0e3e50]"
            >
              Learn more
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </Link>
          </div>

          {/* Dashboard Preview */}
          <div className="relative max-w-5xl mx-auto">
            <div className="rounded-lg overflow-hidden shadow-xl border border-gray-200">
              <Image 
                src="/images/landing.png" 
                alt="Ascension Dashboard" 
                width={1200} 
                height={675} 
                className="w-full h-auto"
                priority
              />
            </div>
          </div>
        </div>
      </main>

      {/* Footer (Optional - good practice) */}
      <footer className="py-10 bg-gray-800 text-gray-300 text-center text-sm">
        <div className="container mx-auto">
          <p>&copy; {new Date().getFullYear()} Ascension. All rights reserved.</p>
          {/* <div className="mt-2 space-x-4">
            <Link href="/privacy" className="hover:text-white">Privacy Policy</Link>
            <Link href="/terms" className="hover:text-white">Terms of Service</Link>
          </div> */}
        </div>
      </footer> 
    </div>
  );
}

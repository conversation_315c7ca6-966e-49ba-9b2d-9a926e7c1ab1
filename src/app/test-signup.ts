// Simple test script to check the signup endpoint
const testSignup = async () => {
  const API_URL = 'https://dev.ascensionservices.net/api/v1/auth';
  
  const testData = {
    org_in: {
      name: 'Test Company ' + Math.random().toString(36).substring(2, 8),
      type: 'company',
      address: '123 Test St',
      telephone: '+256700123456',
      contact_person: 'Test User',
      status: 'active',
      country: 'Uganda',
      incorporation_date: new Date().toISOString().split('T')[0]
    },
    admin_in: {
      email: `testuser_${Math.random().toString(36).substring(2, 8)}@example.com`,
      password: 'Test@1234',
      first_name: 'Test',
      last_name: 'User',
      telephone_number: '+256700123456',
      role_names: ['c_admin']
    }
  };

  console.log('Sending test request to:', `${API_URL}/signup`);
  console.log('Test data:', JSON.stringify(testData, null, 2));

  try {
    const response = await fetch(`${API_URL}/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'accept': 'application/json'
      },
      body: JSON.stringify(testData)
    });

    const data = await response.json();
    
    console.log('Response status:', response.status);
    console.log('Response data:', data);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return data;
  } catch (error: unknown) {
    console.error('Test signup failed:', error);
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response?: { data?: unknown } };
      console.error('Error response data:', axiosError.response?.data);
    }
    throw error;
  }
};

// Run the test
testSignup()
  .then(() => console.log('Test completed'))
  .catch(() => console.error('Test failed'));

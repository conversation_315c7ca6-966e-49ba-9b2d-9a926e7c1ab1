'use client';

import React, { useState } from 'react';
import { AppLayout } from '@/components/Layouts/AppLayout';
import AppHeader from '@/components/Layouts/AppHeader';
import AppSidebar from '@/components/Layouts/AppSidebar';
import { AppBreadcrumb } from '@/components/Layouts/AppBreadcrumb';
import { FiSearch, FiFilter, FiDownload, FiEye, FiEdit, FiMoreVertical } from 'react-icons/fi';

// Mock data for pending inquiries
const mockInquiries = [
  {
    id: 1,
    client: 'Uganda National Roads Authority',
    year: 2024,
    caseNo: 173,
    reference: 'UNRA/SUR/S/24-25/173',
    subject: 'Spare parts vehicle UAG 257R',
    status: 'Inquiry',
    dateRequested: 'Sep 14, 2024',
    deliveryRequired: 'Sep 30, 2024'
  },
  {
    id: 2,
    client: 'UNICEF Uganda',
    year: 2024,
    caseNo: 178,
    reference: 'UNICEF/0002/24/178',
    subject: 'Lawn mower',
    status: 'Inquiry',
    dateRequested: 'Sep 12, 2024',
    deliveryRequired: 'Sep 18, 2024'
  },
  {
    id: 3,
    client: 'Serena Resort Munyonyo',
    year: 2024,
    caseNo: 178,
    reference: 'SRM/0002/24/178',
    subject: 'Water pump spares',
    status: 'Inquiry',
    dateRequested: 'Sep 11, 2024',
    deliveryRequired: 'Sep 18, 2024'
  },
  {
    id: 4,
    client: 'Acacia Estates',
    year: 2024,
    caseNo: 177,
    reference: 'ACA/0001/24/177',
    subject: 'Gy Stationery',
    status: 'Inquiry',
    dateRequested: 'Sep 10, 2024',
    deliveryRequired: 'Sep 16, 2024'
  },
  {
    id: 5,
    client: 'UMEME',
    year: 2024,
    caseNo: 176,
    reference: 'UMEME/0002/24/176',
    subject: 'Spark plugs set',
    status: 'Inquiry',
    dateRequested: 'Sep 10, 2024',
    deliveryRequired: 'Sep 30, 2024'
  }
];

export default function PendingInquiriesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<string>('dateRequested');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Filter and sort data
  const filteredInquiries = mockInquiries.filter(inquiry =>
    inquiry.client.toLowerCase().includes(searchTerm.toLowerCase()) ||
    inquiry.reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
    inquiry.subject.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const sortedInquiries = [...filteredInquiries].sort((a, b) => {
    const aValue = a[sortField as keyof typeof a];
    const bValue = b[sortField as keyof typeof b];
    
    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  const totalPages = Math.ceil(sortedInquiries.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedInquiries = sortedInquiries.slice(startIndex, startIndex + itemsPerPage);

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  return (
    <AppLayout>
      <AppHeader />
      <div className="flex flex-1 overflow-hidden">
        <AppSidebar />
        <main className="flex-1 overflow-y-auto bg-gray-50 dark:bg-slate-900">
          <div className="p-6">
            {/* Breadcrumb */}
            <AppBreadcrumb />
            
            {/* Header Section */}
            <div className="flex justify-between items-start mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Pending Inquiries</h1>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                  Manage and track all pending inquiry requests
                </p>
              </div>
              <button className="bg-[#18546c] text-white px-6 py-3 rounded-lg hover:bg-[#134048] dark:bg-[#18546c] dark:hover:bg-[#134048] transition-colors flex items-center font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                <span className="mr-2 text-lg">+</span> Create New
              </button>
            </div>

            {/* Search and Filter Section */}
            <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 p-6 mb-6">
              <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
                <div className="relative flex-1 max-w-md">
                  <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="text"
                    placeholder="Search Client, Year, Subject, Reference..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2.5 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-[#18546c] focus:border-transparent dark:bg-slate-700 dark:text-white"
                  />
                </div>
                <div className="flex gap-3">
                  <button className="flex items-center px-4 py-2.5 border border-gray-300 dark:border-slate-600 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors">
                    <FiFilter className="mr-2 h-4 w-4" />
                    Filter
                  </button>
                  <button className="flex items-center px-4 py-2.5 border border-gray-300 dark:border-slate-600 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors">
                    <FiDownload className="mr-2 h-4 w-4" />
                    Export
                  </button>
                </div>
              </div>
            </div>

            {/* Table Section */}
            <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-slate-700">
                  <thead className="bg-gray-50 dark:bg-slate-700">
                    <tr>
                      <th 
                        scope="col" 
                        className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-200 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-slate-600 transition-colors"
                        onClick={() => handleSort('client')}
                      >
                        Client
                      </th>
                      <th 
                        scope="col" 
                        className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-200 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-slate-600 transition-colors"
                        onClick={() => handleSort('year')}
                      >
                        Year
                      </th>
                      <th 
                        scope="col" 
                        className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-200 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-slate-600 transition-colors"
                        onClick={() => handleSort('caseNo')}
                      >
                        Case No.
                      </th>
                      <th 
                        scope="col" 
                        className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-200 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-slate-600 transition-colors"
                        onClick={() => handleSort('reference')}
                      >
                        Reference
                      </th>
                      <th 
                        scope="col" 
                        className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-200 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-slate-600 transition-colors"
                        onClick={() => handleSort('subject')}
                      >
                        Subject
                      </th>
                      <th 
                        scope="col" 
                        className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-200 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-slate-600 transition-colors"
                        onClick={() => handleSort('status')}
                      >
                        Status
                      </th>
                      <th 
                        scope="col" 
                        className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-200 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-slate-600 transition-colors"
                        onClick={() => handleSort('dateRequested')}
                      >
                        Date requested
                      </th>
                      <th 
                        scope="col" 
                        className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-200 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-slate-600 transition-colors"
                        onClick={() => handleSort('deliveryRequired')}
                      >
                        Delivery required
                      </th>
                      <th scope="col" className="px-6 py-4 text-right text-xs font-semibold text-gray-600 dark:text-gray-200 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-slate-700">
                    {paginatedInquiries.map((inquiry) => (
                      <tr key={inquiry.id} className="hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">{inquiry.client}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 dark:text-white">{inquiry.year}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 dark:text-white">{inquiry.caseNo}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-blue-600 dark:text-blue-400 font-medium">{inquiry.reference}</div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900 dark:text-white">{inquiry.subject}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400">
                            {inquiry.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 dark:text-white">{inquiry.dateRequested}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 dark:text-white">{inquiry.deliveryRequired}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right">
                          <div className="flex items-center justify-end gap-2">
                            <button className="text-gray-400 hover:text-[#18546c] transition-colors p-1">
                              <FiEye className="h-4 w-4" />
                            </button>
                            <button className="text-gray-400 hover:text-[#18546c] transition-colors p-1">
                              <FiEdit className="h-4 w-4" />
                            </button>
                            <button className="text-gray-400 hover:text-[#18546c] transition-colors p-1">
                              <FiMoreVertical className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              {/* Pagination */}
              <div className="bg-white dark:bg-slate-800 px-6 py-4 border-t border-gray-200 dark:border-slate-700">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-700 dark:text-gray-300">
                    Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, sortedInquiries.length)} of {sortedInquiries.length} results
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="px-3 py-1 text-sm border border-gray-300 dark:border-slate-600 rounded hover:bg-gray-50 dark:hover:bg-slate-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Page {currentPage} of {totalPages}
                    </span>
                    <button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="px-3 py-1 text-sm border border-gray-300 dark:border-slate-600 rounded hover:bg-gray-50 dark:hover:bg-slate-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </AppLayout>
  );
}

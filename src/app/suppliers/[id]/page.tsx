'use client';

import { usePara<PERSON>, useRouter } from 'next/navigation';
import { FiArrowLeft, FiDownload, FiEdit, FiTrash2, FiMail, FiPhone, FiMapPin, FiGlobe } from 'react-icons/fi';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'; // Note: Capital 'C' in Card

// Mock data - replace with actual data fetching
const mockSupplier = {
  id: 'OKISTA01',
  name: 'Okinawa Stationers Ltd',
  code: 'OKISTA01',
  category: 'Office Supplies',
  status: 'Active',
  region: 'Central',
  location: 'Kampala',
  contactPerson: '<PERSON>',
  email: '<EMAIL>',
  phone: '+256 700 123456',
  website: 'https://okinawastationers.biz',
  products: 'Office stationery, school supplies, office furniture',
  brands: 'Bic, Oxford, 3M, Durable',
  coverage: ['Central Region', 'Eastern Region'],
  leadTime: '2-4 days',
  businessCategories: ['General Merchandise', 'ICT Hardware & Software', 'Electricals & Electronics'],
  catalogUrl: '/catalogs/2023-catalogue.pdf'
};

export default function SupplierDetailsPage() {
  const router = useRouter();
  const { id } = useParams();
  
  // In a real app, you would fetch the supplier data here
  // const { data: supplier, isLoading } = useGetSupplierById(id);
  const supplier = mockSupplier; // Using mock data for now

  if (!supplier) {
    return (
      <div className="p-6">
        <Button 
          variant="ghost" 
          onClick={() => router.back()}
          className="mb-6"
        >
          <FiArrowLeft className="mr-2 h-4 w-4" />
          Back to Suppliers
        </Button>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold">Supplier not found</h2>
          <p className="text-muted-foreground mt-2">The requested supplier could not be found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <Button 
          variant="ghost" 
          onClick={() => router.back()}
        >
          <FiArrowLeft className="mr-2 h-4 w-4" />
          Back to Suppliers
        </Button>
        <div className="space-x-2">
          <Button variant="outline" size="sm" className="gap-2">
            <FiEdit className="h-4 w-4" />
            Edit
          </Button>
          <Button variant="outline" size="sm" className="gap-2">
            <FiTrash2 className="h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      <div className="grid gap-6">
        {/* Header */}
        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-2xl font-bold">{supplier.name}</CardTitle>
              <span className="inline-flex items-center rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400">
                {supplier.status}
              </span>
            </div>
            <p className="text-muted-foreground">Supplier Code: {supplier.code}</p>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Category</p>
                <p>{supplier.category}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Region</p>
                <p>{supplier.region} Region</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Location</p>
                <p>{supplier.location}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Delivery Lead Time</p>
                <p>{supplier.leadTime}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content */}
        <div className="grid gap-6 md:grid-cols-3">
          {/* Left Column */}
          <div className="space-y-6 md:col-span-2">
            {/* About */}
            <Card>
              <CardHeader>
                <CardTitle>About</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-medium">Products & Services</h3>
                  <p className="text-muted-foreground">{supplier.products}</p>
                </div>
                <div>
                  <h3 className="font-medium">Key Brands</h3>
                  <p className="text-muted-foreground">{supplier.brands}</p>
                </div>
                <div>
                  <h3 className="font-medium">Coverage Area</h3>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {supplier.coverage.map((area, index) => (
                      <span 
                        key={index}
                        className="inline-flex items-center rounded-full border px-3 py-1 text-xs"
                      >
                        {area}
                      </span>
                    ))}
                  </div>
                </div>
                <div>
                  <h3 className="font-medium">Business Categories</h3>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {supplier.businessCategories.map((category, index) => (
                      <span 
                        key={index}
                        className="inline-flex items-center rounded-full bg-secondary px-3 py-1 text-xs text-secondary-foreground"
                      >
                        {category}
                      </span>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            {/* Contact */}
            <Card>
              <CardHeader>
                <CardTitle>Contact</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="mt-0.5">
                    <FiMail className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Email</p>
                    <a 
                      href={`mailto:${supplier.email}`} 
                      className="text-sm text-primary hover:underline"
                    >
                      {supplier.email}
                    </a>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="mt-0.5">
                    <FiPhone className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Phone</p>
                    <a 
                      href={`tel:${supplier.phone}`} 
                      className="text-sm text-primary hover:underline"
                    >
                      {supplier.phone}
                    </a>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="mt-0.5">
                    <FiMapPin className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Location</p>
                    <p className="text-sm text-muted-foreground">
                      {supplier.location}, {supplier.region} Region
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="mt-0.5">
                    <FiGlobe className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Website</p>
                    <a 
                      href={supplier.website} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-sm text-primary hover:underline"
                    >
                      {supplier.website.replace(/^https?:\/\//, '')}
                    </a>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Documents */}
            <Card>
              <CardHeader>
                <CardTitle>Documents</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="rounded-lg border p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="flex h-10 w-10 items-center justify-center rounded-md bg-blue-50 dark:bg-blue-900/30">
                        <FiDownload className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">2023 Catalogue</p>
                        <p className="text-xs text-muted-foreground">PDF • 2.4 MB</p>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <FiDownload className="h-4 w-4" />
                      <span className="sr-only">Download</span>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

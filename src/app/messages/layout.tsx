"use client";

import { useState, useEffect } from 'react';
import AppHeader from "@/components/Layouts/AppHeader";
import AppSidebar, { MobileMenuButton } from "@/components/Layouts/AppSidebar";
import { Providers } from '../providers';

export default function MessagesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isMobile, setIsMobile] = useState(false);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  // Check if we're on mobile
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth >= 768) {
        setIsMobileSidebarOpen(true);
      }
    };

    checkIfMobile();
    window.addEventListener('resize', checkIfMobile);
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  const toggleMobileSidebar = () => {
    setIsMobileSidebarOpen(!isMobileSidebarOpen);
  };

  const closeMobileSidebar = () => {
    if (isMobile) {
      setIsMobileSidebarOpen(false);
    }
  };

  return (
    <Providers>
      <div className="flex h-screen bg-gray-50 dark:bg-slate-900">
        {/* Mobile sidebar overlay */}
        {isMobileSidebarOpen && (
          <div 
            className="fixed inset-0 bg-black/50 z-40 md:hidden" 
            onClick={closeMobileSidebar}
            aria-hidden="true"
          />
        )}
        
        {/* Sidebar */}
        <AppSidebar 
          isMobileOpen={isMobileSidebarOpen} 
          onMobileClose={closeMobileSidebar} 
        />
        
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex items-center h-16 bg-[#2A6E78]">
            <MobileMenuButton onClick={toggleMobileSidebar} />
            <AppHeader />
          </div>
          <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 dark:bg-slate-900 p-6">
            {children}
          </main>
        </div>
      </div>
    </Providers>
  );
}

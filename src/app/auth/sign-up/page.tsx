import Link from "next/link";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import type { Metadata } from "next";
import Image from "next/image";

export const metadata: Metadata = {
  title: "Sign Up",
};

export default function SignUp() {
  return (
    <>
      <Breadcrumb pageName="Sign Up" />

      <div className="mx-auto w-full max-w-[450px] rounded-[10px] bg-white p-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card md:p-12.5">
        <div className="mb-7.5 text-center">
          <Link className="inline-block" href="/">
            <Image
              className="hidden dark:block"
              src={"/images/logo/logo.svg"}
              alt="Logo"
              width={176}
              height={32}
            />
            <Image
              className="dark:hidden"
              src={"/images/logo/logo-dark.svg"}
              alt="Logo"
              width={176}
              height={32}
            />
          </Link>
          <h1 className="mb-2.5 mt-5 text-2xl font-bold text-dark dark:text-white">
            Create an Account
          </h1>
          <p className="text-dark-4 dark:text-dark-6">
            Fill in the form below to get started
          </p>
        </div>

        {/* Sign Up Form */}
        <form>
          <div className="mb-5.5">
            <label className="mb-3 block text-sm font-medium text-dark dark:text-white">
              Full Name
            </label>
            <input
              type="text"
              placeholder="Enter your full name"
              className="w-full rounded-lg border border-stroke bg-transparent px-5 py-3 text-dark-6 outline-none transition focus:border-primary focus-visible:shadow-none dark:border-dark-3 dark:bg-dark-2 dark:text-white"
            />
          </div>

          <div className="mb-5.5">
            <label className="mb-3 block text-sm font-medium text-dark dark:text-white">
              Email
            </label>
            <input
              type="email"
              placeholder="Enter your email"
              className="w-full rounded-lg border border-stroke bg-transparent px-5 py-3 text-dark-6 outline-none transition focus:border-primary focus-visible:shadow-none dark:border-dark-3 dark:bg-dark-2 dark:text-white"
            />
          </div>

          <div className="mb-5.5">
            <label className="mb-3 block text-sm font-medium text-dark dark:text-white">
              Password
            </label>
            <input
              type="password"
              placeholder="Enter your password"
              className="w-full rounded-lg border border-stroke bg-transparent px-5 py-3 text-dark-6 outline-none transition focus:border-primary focus-visible:shadow-none dark:border-dark-3 dark:bg-dark-2 dark:text-white"
            />
          </div>

          <div className="mb-7.5">
            <label className="mb-3 block text-sm font-medium text-dark dark:text-white">
              Confirm Password
            </label>
            <input
              type="password"
              placeholder="Confirm your password"
              className="w-full rounded-lg border border-stroke bg-transparent px-5 py-3 text-dark-6 outline-none transition focus:border-primary focus-visible:shadow-none dark:border-dark-3 dark:bg-dark-2 dark:text-white"
            />
          </div>


          <button
            type="submit"
            className="flex w-full items-center justify-center rounded-lg bg-primary p-3 font-medium text-white transition hover:bg-opacity-90"
          >
            Create Account
          </button>
        </form>

        <div className="mt-7.5 text-center">
          <p className="text-dark-4 dark:text-dark-6">
            Already have an account?{" "}
            <Link href="/auth/sign-in" className="text-primary">
              Sign In
            </Link>
          </p>
        </div>
      </div>
    </>
  );
}

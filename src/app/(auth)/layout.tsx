import { Metadata } from 'next';
import { Providers } from '../providers';
import '../../css/style.css';

export const metadata: Metadata = {
  title: 'Authentication',
  description: 'Authentication forms for Ascension'
};

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
